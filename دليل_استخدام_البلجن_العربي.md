# دليل استخدام بلجن دعم النصوص العربية في Blender

## نظرة عامة
هذا البلجن يحل مشكلة عرض النصوص العربية في Blender بشكل صحيح، حيث يوفر:
- ربط الحروف العربية بشكل صحيح
- الكتابة من اليمين إلى اليسار (RTL)
- دعم النصوص المختلطة (عربي + إنجليزي + أرقام)
- واجهة سهلة الاستخدام

## التثبيت

### الطريقة الأولى: التثبيت اليدوي
1. انسخ ملف `arabic_text_support.py` إلى مجلد البلجنز في Blender
2. افتح Blender واذهب إلى Edit > Preferences > Add-ons
3. ا<PERSON><PERSON><PERSON> عن "دعم النصوص العربية" وفعله

### الطريقة الثانية: التثبيت عبر الكود (الطريقة المستخدمة)
```python
import sys
sys.path.append("مسار_المجلد_الذي_يحتوي_على_البلجن")
import arabic_text_support
arabic_text_support.register()
```

## الاستخدام

### الوصول إلى البلجن
1. اضغط على `N` لفتح الشريط الجانبي في 3D Viewport
2. ستجد تبويب "Arabic Text" في الشريط الجانبي
3. انقر عليه لفتح لوحة البلجن

### واجهة البلجن

#### حالة المكتبات
- **python-bidi**: لدعم الكتابة ثنائية الاتجاه
- **arabic-reshaper**: لربط الحروف العربية
- إذا لم تكن متوفرة، سيستخدم البلجن خوارزميات بديلة

#### إعدادات النص العربي
- **النص العربي**: أدخل النص العربي المطلوب
- **حجم الخط**: تحكم في حجم النص (0.1 - 10.0)
- **عمق النص**: عمق النص ثلاثي الأبعاد (0.0 - 5.0)
- **ربط الحروف**: تفعيل/إلغاء ربط الحروف العربية
- **من اليمين إلى اليسار**: تفعيل/إلغاء الكتابة من اليمين إلى اليسار

#### الأزرار
- **إنشاء نص عربي جديد**: ينشئ كائن نص جديد بالإعدادات المحددة
- **تحديث النص المحدد**: يحدث النص المحدد حالياً بالإعدادات الجديدة
- **تثبيت المكتبات المطلوبة**: يثبت المكتبات الإضافية لدعم أفضل

## أمثلة الاستخدام

### مثال 1: نص بسيط
```
النص: مرحباً بكم في بلندر
النتيجة: نص عربي مع ربط الحروف والكتابة من اليمين إلى اليسار
```

### مثال 2: نص مع أرقام
```
النص: العام 2024 هو عام التقنية
النتيجة: نص مختلط يحافظ على ترتيب الأرقام الصحيح
```

### مثال 3: نص ديني
```
النص: بسم الله الرحمن الرحيم
النتيجة: نص مقدس بربط حروف صحيح
```

## الميزات التقنية

### ربط الحروف العربية
البلجن يدعم ربط الحروف العربية في أربعة أشكال:
- **منفصل**: الحرف وحده
- **ابتدائي**: الحرف في بداية الكلمة
- **وسطي**: الحرف في وسط الكلمة  
- **نهائي**: الحرف في نهاية الكلمة

### الكتابة ثنائية الاتجاه
- يدعم النصوص المختلطة (عربي + إنجليزي + أرقام)
- يحافظ على الترتيب الصحيح للأرقام والنصوص الإنجليزية
- يطبق قواعد Unicode Bidirectional Algorithm

### الخوارزميات البديلة
في حالة عدم توفر المكتبات الخارجية، يستخدم البلجن خوارزميات بديلة:
- خوارزمية ربط الحروف المبسطة
- خوارزمية الكتابة ثنائية الاتجاه المحسنة

## نصائح للاستخدام

### للحصول على أفضل النتائج
1. استخدم خطوط عربية مناسبة في Blender
2. فعل كلاً من "ربط الحروف" و "من اليمين إلى اليسار"
3. اضبط حجم الخط والعمق حسب احتياجاتك

### حل المشاكل الشائعة
- **النص لا يظهر بشكل صحيح**: تأكد من تفعيل جميع الخيارات
- **الحروف غير مربوطة**: تأكد من تفعيل "ربط الحروف"
- **النص معكوس**: تأكد من تفعيل "من اليمين إلى اليسار"

## تحسين الأداء

### تثبيت المكتبات الإضافية
لأفضل أداء ودقة، يُنصح بتثبيت:
```bash
pip install python-bidi arabic-reshaper
```

أو استخدم زر "تثبيت المكتبات المطلوبة" في البلجن

## الدعم والتطوير

### الإبلاغ عن المشاكل
إذا واجهت أي مشاكل:
1. تأكد من إصدار Blender (3.0+)
2. تحقق من رسائل الخطأ في Console
3. جرب النص مع إعدادات مختلفة

### التطوير المستقبلي
- دعم المزيد من اللغات (فارسي، أردو)
- تحسين خوارزميات الربط
- إضافة خطوط عربية مدمجة
- دعم التشكيل العربي

## الخلاصة
هذا البلجن يوفر حلاً شاملاً لمشكلة النصوص العربية في Blender، مما يتيح للمستخدمين العرب إنشاء محتوى ثلاثي الأبعاد بنصوص عربية صحيحة ومقروءة.

---
**تم تطوير هذا البلجن لخدمة المجتمع العربي في مجال التصميم ثلاثي الأبعاد** 🎨✨
