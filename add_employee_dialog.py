from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLineEdit,
                               QLabel, QPushButton, QDateEdit, QDoubleSpinBox,
                               QComboBox, QMessageBox)
from PySide6.QtCore import Qt, QDate

class AddEmployeeDialog(QDialog):
    """نافذة حوار إضافة/تعديل موظف"""
    
    def __init__(self, parent=None, employee_data=None):
        super().__init__(parent)
        self.employee_data = employee_data
        self.setup_ui()
        if employee_data:
            self.load_employee_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة موظف جديد" if not self.employee_data else "تعديل بيانات موظف")
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # الاسم
        name_layout = QHBoxLayout()
        self.name_input = QLineEdit()
        name_layout.addWidget(QLabel("الاسم:"))
        name_layout.addWidget(self.name_input)
        
        # رقم الهاتف
        phone_layout = QHBoxLayout()
        self.phone_input = QLineEdit()
        phone_layout.addWidget(QLabel("رقم الهاتف:"))
        phone_layout.addWidget(self.phone_input)
        
        # العنوان
        address_layout = QHBoxLayout()
        self.address_input = QLineEdit()
        address_layout.addWidget(QLabel("العنوان:"))
        address_layout.addWidget(self.address_input)
        
        # تاريخ التوظيف
        hire_date_layout = QHBoxLayout()
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setDate(QDate.currentDate())
        hire_date_layout.addWidget(QLabel("تاريخ التوظيف:"))
        hire_date_layout.addWidget(self.hire_date_input)
        
        # الراتب
        salary_layout = QHBoxLayout()
        self.salary_input = QDoubleSpinBox()
        self.salary_input.setRange(0, 1000000)
        self.salary_input.setSingleStep(100)
        salary_layout.addWidget(QLabel("الراتب:"))
        salary_layout.addWidget(self.salary_input)
        
        # الحالة
        status_layout = QHBoxLayout()
        self.status_input = QComboBox()
        self.status_input.addItems(["نشط", "غير نشط"])
        status_layout.addWidget(QLabel("الحالة:"))
        status_layout.addWidget(self.status_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        # إضافة كل العناصر للتخطيط الرئيسي
        layout.addLayout(name_layout)
        layout.addLayout(phone_layout)
        layout.addLayout(address_layout)
        layout.addLayout(hire_date_layout)
        layout.addLayout(salary_layout)
        layout.addLayout(status_layout)
        layout.addLayout(buttons_layout)
        
    def load_employee_data(self):
        """تحميل بيانات الموظف للتعديل"""
        if not self.employee_data:
            return

        # التحقق من وجود البيانات قبل استخدامها
        if 'name' in self.employee_data:
            self.name_input.setText(str(self.employee_data['name']))
            
        if 'phone' in self.employee_data:
            self.phone_input.setText(str(self.employee_data['phone']))
            
        if 'address' in self.employee_data:
            self.address_input.setText(str(self.employee_data['address']))
            
        if 'hire_date' in self.employee_data and self.employee_data['hire_date']:
            try:
                date = QDate.fromString(str(self.employee_data['hire_date']), 'yyyy-MM-dd')
                if date.isValid():
                    self.hire_date_input.setDate(date)
            except:
                self.hire_date_input.setDate(QDate.currentDate())
            
        if 'salary' in self.employee_data:
            try:
                self.salary_input.setValue(float(self.employee_data['salary']))
            except (ValueError, TypeError):
                self.salary_input.setValue(0.0)
            
        if 'status' in self.employee_data and self.employee_data['status']:
            self.status_input.setCurrentText(str(self.employee_data['status']))
        
    def get_employee_data(self):
        """الحصول على بيانات الموظف المدخلة"""
        return {
            'name': self.name_input.text(),
            'phone': self.phone_input.text(),
            'address': self.address_input.text(),
            'hire_date': self.hire_date_input.date().toString('yyyy-MM-dd'),
            'salary': self.salary_input.value(),
            'status': self.status_input.currentText()
        }
        
    def accept(self):
        """التحقق من البيانات قبل الحفظ"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم الموظف")
            return
        if not self.phone_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال رقم الهاتف")
            return
            
        super().accept() 