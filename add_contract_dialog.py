from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit,
    QMessageBox, QTextEdit
)
from PySide6.QtCore import Qt, QDate
import sqlite3

class AddContractDialog(QDialog):
    def __init__(self, db, contract_data=None, parent=None):
        super().__init__(parent)
        self.db = db
        self.contract_data = contract_data
        self.setup_ui()
        
        if contract_data:
            self.setWindowTitle("تعديل عقد")
            self.load_contract_data()
        else:
            self.setWindowTitle("إضافة عقد جديد")
            
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # رقم العقد
        contract_number_layout = QHBoxLayout()
        self.contract_number_input = QLineEdit()
        contract_number_layout.addWidget(QLabel("رقم العقد:"))
        contract_number_layout.addWidget(self.contract_number_input)
        
        # العميل
        customer_layout = QHBoxLayout()
        self.customer_combo = QComboBox()
        customer_layout.addWidget(QLabel("العميل:"))
        customer_layout.addWidget(self.customer_combo)
        
        # الفرع البنكي
        bank_layout = QHBoxLayout()
        self.bank_combo = QComboBox()
        bank_layout.addWidget(QLabel("الفرع البنكي:"))
        bank_layout.addWidget(self.bank_combo)
        
        # الفاتورة
        invoice_layout = QHBoxLayout()
        self.invoice_combo = QComboBox()
        invoice_layout.addWidget(QLabel("الفاتورة:"))
        invoice_layout.addWidget(self.invoice_combo)
        
        # المبلغ الإجمالي
        total_amount_layout = QHBoxLayout()
        self.total_amount_input = QDoubleSpinBox()
        self.total_amount_input.setMaximum(**********)
        self.total_amount_input.setMinimum(0)
        total_amount_layout.addWidget(QLabel("المبلغ الإجمالي:"))
        total_amount_layout.addWidget(self.total_amount_input)
        
        # عدد الأقساط
        installments_layout = QHBoxLayout()
        self.installments_input = QSpinBox()
        self.installments_input.setMinimum(1)
        self.installments_input.setMaximum(120)  # 10 سنوات كحد أقصى
        installments_layout.addWidget(QLabel("عدد الأقساط:"))
        installments_layout.addWidget(self.installments_input)
        
        # قيمة القسط
        installment_amount_layout = QHBoxLayout()
        self.installment_amount_input = QDoubleSpinBox()
        self.installment_amount_input.setMaximum(**********)
        self.installment_amount_input.setMinimum(0)
        installment_amount_layout.addWidget(QLabel("قيمة القسط:"))
        installment_amount_layout.addWidget(self.installment_amount_input)
        
        # تاريخ البداية
        start_date_layout = QHBoxLayout()
        self.start_date_input = QDateEdit()
        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setDate(QDate.currentDate())
        start_date_layout.addWidget(QLabel("تاريخ البداية:"))
        start_date_layout.addWidget(self.start_date_input)
        
        # تاريخ النهاية
        end_date_layout = QHBoxLayout()
        self.end_date_input = QDateEdit()
        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setDate(QDate.currentDate().addMonths(1))
        end_date_layout.addWidget(QLabel("تاريخ النهاية:"))
        end_date_layout.addWidget(self.end_date_input)
        
        # الحالة
        status_layout = QHBoxLayout()
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "منتهي", "ملغي"])
        status_layout.addWidget(QLabel("الحالة:"))
        status_layout.addWidget(self.status_combo)
        
        # ملاحظات
        notes_layout = QHBoxLayout()
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        notes_layout.addWidget(QLabel("ملاحظات:"))
        notes_layout.addWidget(self.notes_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        # إضافة كل العناصر للتخطيط الرئيسي
        layout.addLayout(contract_number_layout)
        layout.addLayout(customer_layout)
        layout.addLayout(bank_layout)
        layout.addLayout(invoice_layout)
        layout.addLayout(total_amount_layout)
        layout.addLayout(installments_layout)
        layout.addLayout(installment_amount_layout)
        layout.addLayout(start_date_layout)
        layout.addLayout(end_date_layout)
        layout.addLayout(status_layout)
        layout.addLayout(notes_layout)
        layout.addLayout(buttons_layout)
        
        # ربط الإشارات
        self.total_amount_input.valueChanged.connect(self.calculate_installment_amount)
        self.installments_input.valueChanged.connect(self.calculate_installment_amount)
        self.start_date_input.dateChanged.connect(self.update_end_date)
        self.installments_input.valueChanged.connect(self.update_end_date)
        
        # تحميل البيانات
        self.load_customers()
        self.load_bank_branches()
        self.load_invoices()
        
    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            customers = self.db.execute("SELECT id, name FROM customers ORDER BY name").fetchall()
            self.customer_combo.clear()
            self.customer_combo.addItem("- اختر عميلاً -", None)
            
            for customer_id, name in customers:
                self.customer_combo.addItem(name, customer_id)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل قائمة العملاء: {str(e)}")
            
    def load_bank_branches(self):
        """تحميل قائمة الفروع البنكية"""
        try:
            branches = self.db.execute("""
                SELECT bb.id, b.name || ' - ' || bb.name as full_name 
                FROM bank_branches bb 
                JOIN banks b ON b.id = bb.main_bank_id 
                ORDER BY b.name, bb.name
            """).fetchall()
            
            self.bank_combo.clear()
            self.bank_combo.addItem("- اختر فرعاً -", None)
            
            for branch_id, full_name in branches:
                self.bank_combo.addItem(full_name, branch_id)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل قائمة الفروع البنكية: {str(e)}")
            
    def load_invoices(self):
        """تحميل قائمة الفواتير غير المرتبطة بعقود"""
        try:
            # استثناء الفاتورة الحالية إذا كنا في وضع التعديل
            exclude_invoice = ""
            if self.contract_data:
                exclude_invoice = f"AND i.id != {self.contract_data['invoice_id']}"
                
            invoices = self.db.execute(f"""
                SELECT i.id, i.invoice_number, i.total_amount 
                FROM invoices i 
                LEFT JOIN contracts c ON c.invoice_id = i.id 
                WHERE c.id IS NULL {exclude_invoice}
                ORDER BY i.invoice_date DESC
            """).fetchall()
            
            self.invoice_combo.clear()
            self.invoice_combo.addItem("- اختر فاتورة -", None)
            
            for invoice_id, number, amount in invoices:
                self.invoice_combo.addItem(f"{number} - {amount} ريال", invoice_id)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل قائمة الفواتير: {str(e)}")
            
    def calculate_installment_amount(self):
        """حساب قيمة القسط الشهري"""
        total = self.total_amount_input.value()
        installments = self.installments_input.value()
        
        if installments > 0:
            amount = total / installments
            self.installment_amount_input.setValue(amount)
            
    def update_end_date(self):
        """تحديث تاريخ نهاية العقد بناءً على عدد الأقساط"""
        start_date = self.start_date_input.date()
        installments = self.installments_input.value()
        
        end_date = start_date.addMonths(installments - 1)
        self.end_date_input.setDate(end_date)
        
    def load_contract_data(self):
        """تحميل بيانات العقد للتعديل"""
        if not self.contract_data:
            return

        # التحقق من وجود البيانات قبل استخدامها
        if 'contract_number' in self.contract_data:
            self.contract_number_input.setText(str(self.contract_data['contract_number']))
        
        if 'customer_id' in self.contract_data:
            customer_index = self.customer_combo.findData(self.contract_data['customer_id'])
            if customer_index >= 0:
                self.customer_combo.setCurrentIndex(customer_index)
            
        if 'bank_branch_id' in self.contract_data:
            branch_index = self.bank_combo.findData(self.contract_data['bank_branch_id'])
            if branch_index >= 0:
                self.bank_combo.setCurrentIndex(branch_index)
            
        if 'invoice_id' in self.contract_data:
            invoice_index = self.invoice_combo.findData(self.contract_data['invoice_id'])
            if invoice_index >= 0:
                self.invoice_combo.setCurrentIndex(invoice_index)
            
        if 'total_amount' in self.contract_data:
            try:
                self.total_amount_input.setValue(float(self.contract_data['total_amount']))
            except (ValueError, TypeError):
                self.total_amount_input.setValue(0.0)
        
        if 'number_of_installments' in self.contract_data:
            try:
                self.installments_input.setValue(int(self.contract_data['number_of_installments']))
            except (ValueError, TypeError):
                self.installments_input.setValue(1)
        
        if 'installment_amount' in self.contract_data:
            try:
                self.installment_amount_input.setValue(float(self.contract_data['installment_amount']))
            except (ValueError, TypeError):
                self.installment_amount_input.setValue(0.0)
        
        if 'start_date' in self.contract_data and self.contract_data['start_date']:
            try:
                date = QDate.fromString(str(self.contract_data['start_date']), 'yyyy-MM-dd')
                if date.isValid():
                    self.start_date_input.setDate(date)
            except:
                self.start_date_input.setDate(QDate.currentDate())
        
        if 'end_date' in self.contract_data and self.contract_data['end_date']:
            try:
                date = QDate.fromString(str(self.contract_data['end_date']), 'yyyy-MM-dd')
                if date.isValid():
                    self.end_date_input.setDate(date)
            except:
                self.end_date_input.setDate(QDate.currentDate().addMonths(1))
        
        if 'status' in self.contract_data and self.contract_data['status']:
            status_index = self.status_combo.findText(str(self.contract_data['status']))
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)
            
        if 'notes' in self.contract_data and self.contract_data['notes']:
            self.notes_input.setText(str(self.contract_data['notes']))
        
    def get_contract_data(self):
        """الحصول على بيانات العقد المدخلة"""
        return {
            'contract_number': self.contract_number_input.text(),
            'customer_id': self.customer_combo.currentData(),
            'bank_branch_id': self.bank_combo.currentData(),
            'invoice_id': self.invoice_combo.currentData(),
            'total_amount': self.total_amount_input.value(),
            'installment_amount': self.installment_amount_input.value(),
            'number_of_installments': self.installments_input.value(),
            'start_date': self.start_date_input.date().toString('yyyy-MM-dd'),
            'end_date': self.end_date_input.date().toString('yyyy-MM-dd'),
            'status': self.status_combo.currentText(),
            'notes': self.notes_input.toPlainText()
        }
        
    def accept(self):
        """التحقق من البيانات قبل الحفظ"""
        if not self.contract_number_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال رقم العقد")
            return
            
        if not self.customer_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار العميل")
            return
            
        if not self.bank_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار الفرع البنكي")
            return
            
        if not self.invoice_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار الفاتورة")
            return
            
        if self.total_amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يجب إدخال المبلغ الإجمالي")
            return
            
        if self.installments_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يجب إدخال عدد الأقساط")
            return
            
        if self.installment_amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يجب إدخال قيمة القسط")
            return
            
        super().accept() 