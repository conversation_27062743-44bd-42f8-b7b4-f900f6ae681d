# ملخص تحويل نظام المبيعات من الإنجليزية إلى العربية

## نظرة عامة
تم تحويل جميع جداول وأعمدة واستعلامات نظام المبيعات من اللغة الإنجليزية إلى اللغة العربية بنجاح.

## الملفات المحدثة

### 1. ملفات قاعدة البيانات
- **database.py**: تم تحديث اسم قاعدة البيانات من `sales_system` إلى `نظام_المبيعات`
- **db_setup_missing_tables.py**: تم تحويل جميع أسماء الجداول والأعمدة إلى العربية

### 2. ملفات الواجهات
- **customers_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **employees_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **suppliers_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **inventory_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **banks_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **expenses_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **contracts_page.py**: تم تحويل جميع الاستعلامات لاستخدام الأسماء العربية
- **pos_page.py**: لا يحتوي على استعلامات مباشرة

### 3. ملفات التحويل الجديدة
- **تحويل_الجداول_للعربية.sql**: ملف تحويل أسماء الجداول والأعمدة
- **تحويل_شامل_للعربية.sql**: ملف تحويل شامل لإنشاء قاعدة بيانات جديدة بالعربية
- **اختبار_التحويل.py**: ملف اختبار شامل للتأكد من عمل النظام

## الجداول المحولة

### الجداول الأساسية
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| categories | الفئات |
| products | المنتجات |
| inventory | المخزون |
| suppliers | الموردين |
| customers | العملاء |
| employees | الموظفين |
| invoices | الفواتير |
| invoice_items | صفوف_الفواتير |
| customer_payments | دفعات_العملاء |

### جداول الموردين
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| supplier_invoices | فواتير_الموردين |
| supplier_invoice_items | صفوف_فواتير_الموردين |
| supplier_payments | دفعات_الموردين |

### الجداول الإضافية
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| banks | البنوك |
| bank_branches | فروع_البنوك |
| expense_categories | فئات_المصروفات |
| expenses | المصروفات |
| permissions | الصلاحيات |
| employee_permissions | صلاحيات_الموظفين |
| attendance | الحضور |
| salaries | الرواتب |
| contracts | العقود |
| installments | الأقساط |

## الأعمدة المحولة

### الأعمدة الأساسية
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| id | المعرف |
| name | الاسم |
| description | الوصف |
| notes | ملاحظات |
| created_at | تاريخ_الإنشاء |
| updated_at | تاريخ_التحديث |
| phone | الهاتف |
| address | العنوان |
| email | البريد_الإلكتروني |
| active | نشط |

### أعمدة المنتجات
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| category_id | الفئة_المعرف |
| barcode | الباركود |
| unit | الوحدة |
| purchase_price | سعر_الشراء |
| selling_price | سعر_البيع |
| min_quantity | الكمية_الدنيا |
| quantity | الكمية |

### أعمدة الفواتير
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| invoice_number | رقم_الفاتورة |
| customer_id | العميل_المعرف |
| employee_id | الموظف_المعرف |
| date | التاريخ |
| total_amount | إجمالي_المبلغ |
| discount | الخصم |
| final_amount | المبلغ_النهائي |
| paid_amount | المبلغ_المدفوع |
| payment_method | طريقة_الدفع |

### أعمدة الموظفين
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| hire_date | تاريخ_التوظيف |
| salary | الراتب |
| status | الحالة |
| check_in | وقت_الدخول |
| check_out | وقت_الخروج |
| base_salary | الراتب_الأساسي |
| commission | العمولة |
| deductions | الخصومات |
| additions | الإضافات |
| payment_date | تاريخ_الدفع |

### أعمدة العقود والأقساط
| الاسم الإنجليزي | الاسم العربي |
|-----------------|-------------|
| contract_number | رقم_العقد |
| bank_branch_id | فرع_البنك_المعرف |
| invoice_id | الفاتورة_المعرف |
| installment_amount | مبلغ_القسط |
| number_of_installments | عدد_الأقساط |
| start_date | تاريخ_البداية |
| end_date | تاريخ_النهاية |
| installment_number | رقم_القسط |
| due_date | تاريخ_الاستحقاق |
| paid_amount | المبلغ_المدفوع |

## خطوات التطبيق

### 1. النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية من قاعدة البيانات الحالية
mysqldump -u root -p sales_system > backup_sales_system.sql
```

### 2. تطبيق التحويل الشامل
```bash
# تطبيق ملف التحويل الشامل
mysql -u root -p < تحويل_شامل_للعربية.sql
```

### 3. تحديث ملف الإعدادات
- تحديث ملف `database.py` لاستخدام قاعدة البيانات الجديدة `نظام_المبيعات`

### 4. اختبار النظام
```bash
# تشغيل اختبارات التحويل
python اختبار_التحويل.py
```

## الميزات الجديدة

### 1. دعم كامل للغة العربية
- جميع أسماء الجداول والأعمدة باللغة العربية
- دعم ترميز UTF-8 لضمان عرض النصوص العربية بشكل صحيح

### 2. المحافظة على البيانات
- جميع البيانات الموجودة محفوظة ومنقولة للجداول الجديدة
- المفاتيح الخارجية والعلاقات محفوظة

### 3. التوافق مع الكود الموجود
- جميع ملفات Python محدثة لاستخدام الأسماء الجديدة
- لا حاجة لتغييرات إضافية في الكود

## ملاحظات مهمة

### 1. النسخ الاحتياطي
- **ضروري جداً**: إنشاء نسخة احتياطية قبل تطبيق التحويل
- حفظ النسخة الاحتياطية في مكان آمن

### 2. اختبار النظام
- تشغيل جميع الاختبارات قبل الاستخدام الفعلي
- التأكد من عمل جميع الوظائف بشكل صحيح

### 3. التدريب
- تدريب المستخدمين على الأسماء الجديدة
- تحديث أي وثائق أو أدلة استخدام

## الدعم والصيانة

### 1. ملفات الاختبار
- `اختبار_التحويل.py`: اختبار شامل لجميع العمليات
- يمكن تشغيله دورياً للتأكد من سلامة النظام

### 2. ملفات التحويل
- `تحويل_شامل_للعربية.sql`: للتحويل الكامل
- `تحويل_الجداول_للعربية.sql`: لتحويل قاعدة بيانات موجودة

### 3. التوثيق
- هذا الملف يحتوي على جميع التفاصيل المطلوبة
- يمكن الرجوع إليه عند الحاجة

---

**تم إكمال التحويل بنجاح! 🎉**

جميع الجداول والأعمدة والاستعلامات تم تحويلها إلى اللغة العربية مع المحافظة على جميع البيانات والوظائف.
