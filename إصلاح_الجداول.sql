-- إصلاح الجداول وإضافة الأعمدة المفقودة

USE نظام_المبيعات;

-- إضافة الأعمدة المفقودة في جدول المنتجات
ALTER TABLE المنتجات ADD COLUMN IF NOT EXISTS الفئة_المعرف INT AFTER الاسم;
ALTER TABLE المنتجات ADD COLUMN IF NOT EXISTS الكمية INT DEFAULT 0 AFTER الكمية_الدنيا;
ALTER TABLE المنتجات ADD COLUMN IF NOT EXISTS نشط BOOLEAN DEFAULT 1 AFTER الكمية;

-- إضافة الأعمدة المفقودة في جدول الموردين
ALTER TABLE الموردين ADD COLUMN IF NOT EXISTS نشط BOOLEAN DEFAULT 1 AFTER ملاحظات;

-- إضافة الأعمدة المفقودة في جدول العملاء
ALTER TABLE العملاء ADD COLUMN IF NOT EXISTS إثبات_الشخصية VARCHAR(100) AFTER العنوان;
ALTER TABLE العملاء ADD COLUMN IF NOT EXISTS البنك VARCHAR(255) AFTER إثبات_الشخصية;
ALTER TABLE العملاء ADD COLUMN IF NOT EXISTS رقم_الحساب VARCHAR(100) AFTER البنك;

-- إضافة الأعمدة المفقودة في جدول الموظفين
ALTER TABLE الموظفين ADD COLUMN IF NOT EXISTS تاريخ_التوظيف DATE AFTER العنوان;
ALTER TABLE الموظفين ADD COLUMN IF NOT EXISTS الراتب DECIMAL(10,2) DEFAULT 0 AFTER تاريخ_التوظيف;
ALTER TABLE الموظفين ADD COLUMN IF NOT EXISTS الحالة ENUM('نشط', 'غير نشط') DEFAULT 'نشط' AFTER الراتب;

-- إضافة الأعمدة المفقودة في جدول الفواتير
ALTER TABLE الفواتير ADD COLUMN IF NOT EXISTS رقم_الفاتورة VARCHAR(50) AFTER المعرف;

-- إضافة المفاتيح الخارجية
ALTER TABLE المنتجات ADD CONSTRAINT FK_المنتجات_الفئة FOREIGN KEY (الفئة_المعرف) REFERENCES الفئات (المعرف) ON DELETE SET NULL;

-- إدراج بعض البيانات التجريبية إذا لم تكن موجودة
INSERT IGNORE INTO الفئات (الاسم, الوصف) VALUES 
('إلكترونيات', 'أجهزة إلكترونية متنوعة'),
('ملابس', 'ملابس رجالية ونسائية'),
('طعام', 'مواد غذائية متنوعة');

INSERT IGNORE INTO المنتجات (الاسم, الفئة_المعرف, الباركود, الوصف, الوحدة, سعر_الشراء, سعر_البيع, الكمية_الدنيا, الكمية, نشط) VALUES 
('هاتف ذكي', 1, '123456789', 'هاتف ذكي حديث', 'قطعة', 500.00, 700.00, 5, 10, 1),
('قميص قطني', 2, '987654321', 'قميص قطني عالي الجودة', 'قطعة', 50.00, 80.00, 10, 25, 1),
('أرز بسمتي', 3, '456789123', 'أرز بسمتي فاخر', 'كيلو', 8.00, 12.00, 50, 100, 1);

SELECT 'تم إصلاح الجداول بنجاح' AS النتيجة;
