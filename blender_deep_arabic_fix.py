#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح عميق للنصوص العربية في واجهة Blender
يعمل على مستوى النظام لإصلاح عرض النصوص العربية في الواجهة
"""

bl_info = {
    "name": "إصلاح النصوص العربية العميق",
    "author": "مطور عربي",
    "version": (2, 0, 0),
    "blender": (3, 0, 0),
    "location": "System Level Arabic Fix",
    "description": "إصلاح عميق لعرض النصوص العربية في واجهة Blender",
    "category": "System",
}

import bpy
import bmesh
import os
import sys
import re
from bpy.app.handlers import persistent

# محاولة استيراد المكتبات العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_LIBS_AVAILABLE = True
    print("✅ المكتبات العربية متوفرة")
except ImportError:
    ARABIC_LIBS_AVAILABLE = False
    print("⚠️ المكتبات العربية غير متوفرة - سيتم استخدام خوارزميات بديلة")

class ArabicTextProcessor:
    """معالج النصوص العربية المتقدم"""
    
    def __init__(self):
        # قاموس شامل للحروف العربية مع جميع أشكالها
        self.arabic_forms = {
            # الألف
            'ا': {'isolated': 'ا', 'initial': 'ا', 'medial': 'ا', 'final': 'ا', 'connects_before': False, 'connects_after': False},
            'أ': {'isolated': 'أ', 'initial': 'أ', 'medial': 'أ', 'final': 'أ', 'connects_before': False, 'connects_after': False},
            'إ': {'isolated': 'إ', 'initial': 'إ', 'medial': 'إ', 'final': 'إ', 'connects_before': False, 'connects_after': False},
            'آ': {'isolated': 'آ', 'initial': 'آ', 'medial': 'آ', 'final': 'آ', 'connects_before': False, 'connects_after': False},
            
            # الباء وعائلتها
            'ب': {'isolated': 'ب', 'initial': 'بـ', 'medial': 'ـبـ', 'final': 'ـب', 'connects_before': True, 'connects_after': True},
            'ت': {'isolated': 'ت', 'initial': 'تـ', 'medial': 'ـتـ', 'final': 'ـت', 'connects_before': True, 'connects_after': True},
            'ث': {'isolated': 'ث', 'initial': 'ثـ', 'medial': 'ـثـ', 'final': 'ـث', 'connects_before': True, 'connects_after': True},
            'ن': {'isolated': 'ن', 'initial': 'نـ', 'medial': 'ـنـ', 'final': 'ـن', 'connects_before': True, 'connects_after': True},
            'ي': {'isolated': 'ي', 'initial': 'يـ', 'medial': 'ـيـ', 'final': 'ـي', 'connects_before': True, 'connects_after': True},
            'ى': {'isolated': 'ى', 'initial': 'ى', 'medial': 'ى', 'final': 'ـى', 'connects_before': True, 'connects_after': False},
            
            # الجيم وعائلتها
            'ج': {'isolated': 'ج', 'initial': 'جـ', 'medial': 'ـجـ', 'final': 'ـج', 'connects_before': True, 'connects_after': True},
            'ح': {'isolated': 'ح', 'initial': 'حـ', 'medial': 'ـحـ', 'final': 'ـح', 'connects_before': True, 'connects_after': True},
            'خ': {'isolated': 'خ', 'initial': 'خـ', 'medial': 'ـخـ', 'final': 'ـخ', 'connects_before': True, 'connects_after': True},
            
            # الدال وعائلتها
            'د': {'isolated': 'د', 'initial': 'د', 'medial': 'د', 'final': 'ـد', 'connects_before': True, 'connects_after': False},
            'ذ': {'isolated': 'ذ', 'initial': 'ذ', 'medial': 'ذ', 'final': 'ـذ', 'connects_before': True, 'connects_after': False},
            
            # الراء والزاي
            'ر': {'isolated': 'ر', 'initial': 'ر', 'medial': 'ر', 'final': 'ـر', 'connects_before': True, 'connects_after': False},
            'ز': {'isolated': 'ز', 'initial': 'ز', 'medial': 'ز', 'final': 'ـز', 'connects_before': True, 'connects_after': False},
            
            # السين والشين
            'س': {'isolated': 'س', 'initial': 'سـ', 'medial': 'ـسـ', 'final': 'ـس', 'connects_before': True, 'connects_after': True},
            'ش': {'isolated': 'ش', 'initial': 'شـ', 'medial': 'ـشـ', 'final': 'ـش', 'connects_before': True, 'connects_after': True},
            
            # الصاد والضاد
            'ص': {'isolated': 'ص', 'initial': 'صـ', 'medial': 'ـصـ', 'final': 'ـص', 'connects_before': True, 'connects_after': True},
            'ض': {'isolated': 'ض', 'initial': 'ضـ', 'medial': 'ـضـ', 'final': 'ـض', 'connects_before': True, 'connects_after': True},
            
            # الطاء والظاء
            'ط': {'isolated': 'ط', 'initial': 'طـ', 'medial': 'ـطـ', 'final': 'ـط', 'connects_before': True, 'connects_after': True},
            'ظ': {'isolated': 'ظ', 'initial': 'ظـ', 'medial': 'ـظـ', 'final': 'ـظ', 'connects_before': True, 'connects_after': True},
            
            # العين والغين
            'ع': {'isolated': 'ع', 'initial': 'عـ', 'medial': 'ـعـ', 'final': 'ـع', 'connects_before': True, 'connects_after': True},
            'غ': {'isolated': 'غ', 'initial': 'غـ', 'medial': 'ـغـ', 'final': 'ـغ', 'connects_before': True, 'connects_after': True},
            
            # الفاء والقاف
            'ف': {'isolated': 'ف', 'initial': 'فـ', 'medial': 'ـفـ', 'final': 'ـف', 'connects_before': True, 'connects_after': True},
            'ق': {'isolated': 'ق', 'initial': 'قـ', 'medial': 'ـقـ', 'final': 'ـق', 'connects_before': True, 'connects_after': True},
            
            # الكاف واللام
            'ك': {'isolated': 'ك', 'initial': 'كـ', 'medial': 'ـكـ', 'final': 'ـك', 'connects_before': True, 'connects_after': True},
            'ل': {'isolated': 'ل', 'initial': 'لـ', 'medial': 'ـلـ', 'final': 'ـل', 'connects_before': True, 'connects_after': True},
            
            # الميم والهاء
            'م': {'isolated': 'م', 'initial': 'مـ', 'medial': 'ـمـ', 'final': 'ـم', 'connects_before': True, 'connects_after': True},
            'ه': {'isolated': 'ه', 'initial': 'هـ', 'medial': 'ـهـ', 'final': 'ـه', 'connects_before': True, 'connects_after': True},
            
            # الواو والتاء المربوطة
            'و': {'isolated': 'و', 'initial': 'و', 'medial': 'و', 'final': 'ـو', 'connects_before': True, 'connects_after': False},
            'ة': {'isolated': 'ة', 'initial': 'ة', 'medial': 'ة', 'final': 'ـة', 'connects_before': True, 'connects_after': False},
            
            # اللام ألف
            'لا': {'isolated': 'لا', 'initial': 'لا', 'medial': 'لا', 'final': 'ـلا', 'connects_before': True, 'connects_after': False},
        }
        
        # نمط للتعرف على النص العربي
        self.arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+')
    
    def is_arabic_text(self, text):
        """فحص ما إذا كان النص يحتوي على أحرف عربية"""
        return bool(self.arabic_pattern.search(text))
    
    def reshape_arabic_text(self, text):
        """ربط الحروف العربية بشكل صحيح"""
        if not text or not self.is_arabic_text(text):
            return text
        
        if ARABIC_LIBS_AVAILABLE:
            try:
                # استخدام المكتبة المتخصصة
                reshaped = arabic_reshaper.reshape(text)
                return reshaped
            except:
                pass
        
        # الخوارزمية البديلة المحسنة
        return self._reshape_fallback(text)
    
    def _reshape_fallback(self, text):
        """خوارزمية ربط الحروف البديلة"""
        if not text:
            return text
        
        result = []
        chars = list(text)
        
        for i, char in enumerate(chars):
            if char in self.arabic_forms:
                letter_info = self.arabic_forms[char]
                
                # فحص الاتصال مع الحروف المجاورة
                connects_to_previous = False
                connects_to_next = False
                
                # فحص الحرف السابق
                if i > 0 and chars[i-1] in self.arabic_forms:
                    prev_letter = self.arabic_forms[chars[i-1]]
                    if prev_letter['connects_after'] and letter_info['connects_before']:
                        connects_to_previous = True
                
                # فحص الحرف التالي
                if i < len(chars) - 1 and chars[i+1] in self.arabic_forms:
                    next_letter = self.arabic_forms[chars[i+1]]
                    if letter_info['connects_after'] and next_letter['connects_before']:
                        connects_to_next = True
                
                # اختيار الشكل المناسب
                if connects_to_previous and connects_to_next:
                    result.append(letter_info['medial'])
                elif connects_to_previous:
                    result.append(letter_info['final'])
                elif connects_to_next:
                    result.append(letter_info['initial'])
                else:
                    result.append(letter_info['isolated'])
            else:
                result.append(char)
        
        return ''.join(result)
    
    def apply_bidi_algorithm(self, text):
        """تطبيق خوارزمية الكتابة ثنائية الاتجاه"""
        if not text or not self.is_arabic_text(text):
            return text
        
        if ARABIC_LIBS_AVAILABLE:
            try:
                # استخدام مكتبة bidi
                return get_display(text)
            except:
                pass
        
        # الخوارزمية البديلة المحسنة
        return self._bidi_fallback(text)
    
    def _bidi_fallback(self, text):
        """خوارزمية RTL بديلة محسنة"""
        if not text:
            return text
        
        # تقسيم النص إلى أجزاء (عربي وغير عربي)
        parts = []
        current_part = ""
        is_arabic_part = False
        
        for char in text:
            char_is_arabic = bool(self.arabic_pattern.match(char))
            
            if char_is_arabic != is_arabic_part:
                if current_part:
                    parts.append((current_part, is_arabic_part))
                current_part = char
                is_arabic_part = char_is_arabic
            else:
                current_part += char
        
        if current_part:
            parts.append((current_part, is_arabic_part))
        
        # عكس الأجزاء العربية
        result_parts = []
        for part_text, is_arabic in parts:
            if is_arabic:
                # عكس النص العربي
                result_parts.append(part_text[::-1])
            else:
                # الاحتفاظ بالنص غير العربي كما هو
                result_parts.append(part_text)
        
        # عكس ترتيب الأجزاء إذا كان النص يبدأ بالعربية
        if parts and parts[0][1]:  # إذا كان الجزء الأول عربي
            result_parts.reverse()
        
        return ''.join(result_parts)
    
    def process_arabic_text(self, text):
        """معالجة شاملة للنص العربي"""
        if not text:
            return text
        
        # الخطوة 1: ربط الحروف
        reshaped_text = self.reshape_arabic_text(text)
        
        # الخطوة 2: تطبيق RTL
        final_text = self.apply_bidi_algorithm(reshaped_text)
        
        return final_text

# إنشاء معالج النصوص العربية العام
arabic_processor = ArabicTextProcessor()

def patch_blender_text_rendering():
    """تصحيح عرض النصوص في Blender على مستوى النظام"""
    
    # تصحيح دالة رسم النصوص في Blender
    original_draw_string = None
    
    try:
        import blf
        
        # حفظ الدالة الأصلية
        if hasattr(blf, 'draw'):
            original_draw_string = blf.draw
            
            def arabic_aware_draw(fontid, text):
                """دالة رسم محسنة للنصوص العربية"""
                if text and arabic_processor.is_arabic_text(text):
                    processed_text = arabic_processor.process_arabic_text(text)
                    return original_draw_string(fontid, processed_text)
                else:
                    return original_draw_string(fontid, text)
            
            # استبدال الدالة
            blf.draw = arabic_aware_draw
            print("✅ تم تصحيح دالة رسم النصوص")
            
    except Exception as e:
        print(f"⚠️ لا يمكن تصحيح دالة الرسم: {e}")

def patch_ui_text_elements():
    """تصحيح عناصر النص في الواجهة"""
    
    # تصحيح نصوص الأزرار والقوائم
    try:
        # البحث عن جميع عناصر الواجهة وتصحيح نصوصها
        for area in bpy.context.screen.areas:
            for region in area.regions:
                # محاولة الوصول لعناصر الواجهة
                try:
                    if hasattr(region, 'draw'):
                        original_draw = region.draw
                        
                        def arabic_aware_region_draw():
                            # تطبيق معالجة النصوص العربية قبل الرسم
                            return original_draw()
                        
                        region.draw = arabic_aware_region_draw
                except:
                    continue
                    
        print("✅ تم تصحيح عناصر الواجهة")
        
    except Exception as e:
        print(f"⚠️ لا يمكن تصحيح عناصر الواجهة: {e}")

@persistent
def arabic_text_handler(scene):
    """معالج النصوص العربية المستمر"""
    try:
        # تطبيق المعالجة على جميع النصوص في المشهد
        for obj in scene.objects:
            if obj.type == 'FONT' and obj.data.body:
                original_text = obj.data.body
                if arabic_processor.is_arabic_text(original_text):
                    # تطبيق المعالجة فقط إذا لم تكن مطبقة مسبقاً
                    processed_text = arabic_processor.process_arabic_text(original_text)
                    if processed_text != original_text:
                        obj.data.body = processed_text
    except:
        pass

class ARABIC_OT_install_deep_fix(bpy.types.Operator):
    """تثبيت الإصلاح العميق للنصوص العربية"""
    bl_idname = "arabic.install_deep_fix"
    bl_label = "تثبيت الإصلاح العميق"
    bl_description = "تثبيت إصلاح عميق لعرض النصوص العربية في الواجهة"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # تثبيت المكتبات المطلوبة
            self.install_arabic_libraries()
            
            # تطبيق التصحيحات
            patch_blender_text_rendering()
            patch_ui_text_elements()
            
            # تسجيل معالج النصوص
            if arabic_text_handler not in bpy.app.handlers.depsgraph_update_post:
                bpy.app.handlers.depsgraph_update_post.append(arabic_text_handler)
            
            # تطبيق إعدادات النظام
            self.apply_system_settings()
            
            self.report({'INFO'}, "تم تثبيت الإصلاح العميق بنجاح - أعد تشغيل Blender")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"خطأ في التثبيت: {str(e)}")
            return {'CANCELLED'}
    
    def install_arabic_libraries(self):
        """تثبيت المكتبات العربية"""
        try:
            import subprocess
            import sys
            
            # تثبيت المكتبات
            libraries = ['python-bidi', 'arabic-reshaper']
            
            for lib in libraries:
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', lib])
                    print(f"✅ تم تثبيت {lib}")
                except:
                    print(f"⚠️ لا يمكن تثبيت {lib}")
                    
        except Exception as e:
            print(f"⚠️ خطأ في تثبيت المكتبات: {e}")
    
    def apply_system_settings(self):
        """تطبيق إعدادات النظام للعربية"""
        prefs = bpy.context.preferences
        
        # تفعيل الخطوط الدولية
        if hasattr(prefs.view, 'use_international_fonts'):
            prefs.view.use_international_fonts = True
        
        # تعيين خط عربي
        arabic_fonts = [
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/arial.ttf",
            "/System/Library/Fonts/Arial.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        ]
        
        for font_path in arabic_fonts:
            if os.path.exists(font_path):
                try:
                    if hasattr(prefs.view, 'font_path_ui'):
                        prefs.view.font_path_ui = font_path
                    print(f"✅ تم تطبيق الخط: {font_path}")
                    break
                except:
                    continue
        
        # تحسين إعدادات النص
        if hasattr(prefs.view, 'ui_scale'):
            prefs.view.ui_scale = 1.1
        
        if hasattr(prefs.view, 'use_text_antialiasing'):
            prefs.view.use_text_antialiasing = True

class ARABIC_OT_test_arabic_display(bpy.types.Operator):
    """اختبار عرض النصوص العربية"""
    bl_idname = "arabic.test_display"
    bl_label = "اختبار العرض العربي"
    bl_description = "اختبار عرض النصوص العربية في الواجهة"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        # نصوص اختبار
        test_texts = [
            "مرحباً بكم في بلندر",
            "النص العربي يجب أن يظهر صحيحاً",
            "الحروف متصلة والاتجاه صحيح",
            "العام 2024 هو عام التقنية"
        ]
        
        print("🧪 اختبار عرض النصوص العربية:")
        print("=" * 40)
        
        for i, text in enumerate(test_texts, 1):
            processed = arabic_processor.process_arabic_text(text)
            print(f"{i}. الأصلي: {text}")
            print(f"   المعالج: {processed}")
            print()
        
        self.report({'INFO'}, "تم اختبار النصوص - راجع وحدة التحكم")
        return {'FINISHED'}

class ARABIC_PT_deep_fix_panel(bpy.types.Panel):
    """لوحة الإصلاح العميق"""
    bl_label = "الإصلاح العميق للعربية"
    bl_idname = "ARABIC_PT_deep_fix_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arabic Interface"
    
    def draw(self, context):
        layout = self.layout
        
        # معلومات الحالة
        box = layout.box()
        box.label(text="حالة المكتبات العربية:")
        status = "متوفرة ✅" if ARABIC_LIBS_AVAILABLE else "غير متوفرة ⚠️"
        box.label(text=f"المكتبات: {status}")
        
        # أزرار التحكم
        layout.separator()
        layout.operator("arabic.install_deep_fix", text="تثبيت الإصلاح العميق")
        layout.operator("arabic.test_display", text="اختبار العرض العربي")
        
        # تحذيرات
        layout.separator()
        box = layout.box()
        box.label(text="تحذيرات مهمة:", icon='ERROR')
        box.label(text="• أعد تشغيل Blender بعد التثبيت")
        box.label(text="• قد يؤثر على أداء البرنامج")
        box.label(text="• اعمل نسخة احتياطية أولاً")

# قائمة الكلاسات
classes = [
    ARABIC_OT_install_deep_fix,
    ARABIC_OT_test_arabic_display,
    ARABIC_PT_deep_fix_panel,
]

def register():
    """تسجيل البلجن"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # تطبيق الإصلاحات تلقائياً
    try:
        patch_blender_text_rendering()
        patch_ui_text_elements()
    except:
        pass
    
    print("تم تفعيل الإصلاح العميق للنصوص العربية")

def unregister():
    """إلغاء تسجيل البلجن"""
    # إزالة معالج النصوص
    if arabic_text_handler in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(arabic_text_handler)
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    print("تم إلغاء تفعيل الإصلاح العميق")

if __name__ == "__main__":
    register()
