#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تصحيح نظام Blender للنصوص العربية على مستوى النظام
يعمل على تصحيح عرض النصوص العربية في جميع أجزاء الواجهة
"""

bl_info = {
    "name": "تصحيح نظام العربية",
    "author": "مطور عربي متقدم",
    "version": (3, 0, 0),
    "blender": (3, 0, 0),
    "location": "System Level",
    "description": "تصحيح شامل لعرض النصوص العربية في نظام Blender",
    "category": "System",
}

import bpy
import os
import sys
import ctypes
from ctypes import wintypes
import locale

# محاولة تحميل مكتبات النظام للتعامل مع النصوص
try:
    if sys.platform == "win32":
        # Windows API للتعامل مع النصوص
        user32 = ctypes.windll.user32
        gdi32 = ctypes.windll.gdi32
        kernel32 = ctypes.windll.kernel32
        
        # تعريف الثوابت
        LOCALE_USER_DEFAULT = 0x0400
        LOCALE_SYSTEM_DEFAULT = 0x0800
        
        print("✅ تم تحميل مكتبات Windows للنصوص")
    else:
        print("⚠️ النظام غير مدعوم بالكامل - سيتم استخدام الحلول البديلة")
        
except Exception as e:
    print(f"⚠️ لا يمكن تحميل مكتبات النظام: {e}")

class SystemArabicPatcher:
    """مصحح النصوص العربية على مستوى النظام"""
    
    def __init__(self):
        self.original_functions = {}
        self.is_patched = False
        
        # قاموس الترجمات المحسن
        self.ui_translations = {
            # القوائم الرئيسية
            "File": "ملف",
            "Edit": "تحرير", 
            "Add": "إضافة",
            "Object": "كائن",
            "Mesh": "شبكة",
            "Curve": "منحنى",
            "Surface": "سطح",
            "Metaball": "كرة معدنية",
            "Text": "نص",
            "Armature": "هيكل عظمي",
            "Lattice": "شبكة",
            "Empty": "فارغ",
            "Image": "صورة",
            "Light": "إضاءة",
            "Light Probe": "مسبار إضاءة",
            "Camera": "كاميرا",
            "Speaker": "مكبر صوت",
            "Force Field": "حقل قوة",
            "Collection": "مجموعة",
            
            # أوضاع العمل
            "Object Mode": "وضع الكائن",
            "Edit Mode": "وضع التحرير",
            "Sculpt Mode": "وضع النحت",
            "Vertex Paint": "طلاء الرؤوس",
            "Weight Paint": "طلاء الأوزان",
            "Texture Paint": "طلاء الخامات",
            "Particle Edit": "تحرير الجسيمات",
            "Pose Mode": "وضع الوضعية",
            
            # الأدوات
            "Select": "تحديد",
            "Select All": "تحديد الكل",
            "Select None": "إلغاء التحديد",
            "Select Invert": "عكس التحديد",
            "Move": "نقل",
            "Rotate": "دوران",
            "Scale": "تكبير",
            "Transform": "تحويل",
            "Duplicate": "نسخ",
            "Delete": "حذف",
            "Extrude": "بثق",
            "Inset": "إدخال",
            "Bevel": "شطف",
            "Loop Cut": "قطع حلقي",
            "Knife": "سكين",
            "Bisect": "تنصيف",
            
            # النوافذ
            "3D Viewport": "منفذ ثلاثي الأبعاد",
            "Image Editor": "محرر الصور",
            "UV Editor": "محرر UV",
            "Shader Editor": "محرر التظليل",
            "Video Sequencer": "محرر الفيديو",
            "Movie Clip Editor": "محرر مقاطع الأفلام",
            "Dope Sheet": "ورقة الحركة",
            "Timeline": "الخط الزمني",
            "Graph Editor": "محرر الرسم البياني",
            "Drivers": "المحركات",
            "Nonlinear Animation": "الحركة غير الخطية",
            "Text Editor": "محرر النصوص",
            "Python Console": "وحدة تحكم Python",
            "Info": "معلومات",
            "Outliner": "المخطط",
            "Properties": "خصائص",
            "File Browser": "متصفح الملفات",
            "Spreadsheet": "جدول البيانات",
            
            # الخصائص
            "Scene": "مشهد",
            "Render": "عرض",
            "World": "عالم",
            "Object": "كائن",
            "Modifier": "معدل",
            "Particle": "جسيم",
            "Physics": "فيزياء",
            "Material": "مادة",
            "Texture": "خامة",
            
            # أزرار شائعة
            "OK": "موافق",
            "Cancel": "إلغاء",
            "Apply": "تطبيق",
            "Reset": "إعادة تعيين",
            "Save": "حفظ",
            "Load": "تحميل",
            "Import": "استيراد",
            "Export": "تصدير",
            "New": "جديد",
            "Open": "فتح",
            "Close": "إغلاق",
            "Quit": "خروج",
            
            # الرسم والعرض
            "Solid": "صلب",
            "Material Preview": "معاينة المادة",
            "Rendered": "معروض",
            "Wireframe": "إطار سلكي",
            
            # الطبقات والمجموعات
            "Layer": "طبقة",
            "Collection": "مجموعة",
            "Group": "مجموعة",
            "Hide": "إخفاء",
            "Show": "إظهار",
            "Lock": "قفل",
            "Unlock": "إلغاء القفل",
        }
    
    def patch_system_locale(self):
        """تصحيح إعدادات النظام للعربية"""
        try:
            if sys.platform == "win32":
                # تعيين الترميز للعربية
                locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
                print("✅ تم تعيين الترميز العربي")
            else:
                # للأنظمة الأخرى
                os.environ['LANG'] = 'ar_SA.UTF-8'
                os.environ['LC_ALL'] = 'ar_SA.UTF-8'
                print("✅ تم تعيين متغيرات البيئة العربية")
                
        except Exception as e:
            print(f"⚠️ لا يمكن تعيين الترميز العربي: {e}")
    
    def patch_blender_ui_system(self):
        """تصحيح نظام الواجهة في Blender"""
        try:
            # تصحيح نظام الترجمة
            if hasattr(bpy.app, 'translations'):
                # إضافة الترجمات العربية
                for english, arabic in self.ui_translations.items():
                    try:
                        bpy.app.translations.register("ar_SA", {
                            ("*", english): arabic
                        })
                    except:
                        continue
                
                print("✅ تم تسجيل الترجمات العربية في النظام")
            
            # تفعيل الترجمة
            prefs = bpy.context.preferences
            if hasattr(prefs.view, 'language'):
                prefs.view.language = 'ar_SA'
                print("✅ تم تفعيل اللغة العربية")
                
        except Exception as e:
            print(f"⚠️ خطأ في تصحيح نظام الواجهة: {e}")
    
    def patch_text_rendering_engine(self):
        """تصحيح محرك رسم النصوص"""
        try:
            import blf
            
            # حفظ الدوال الأصلية
            if 'blf_draw' not in self.original_functions:
                self.original_functions['blf_draw'] = blf.draw
            
            def enhanced_arabic_draw(fontid, text):
                """دالة رسم محسنة للنصوص العربية"""
                if not text:
                    return self.original_functions['blf_draw'](fontid, text)
                
                # فحص النص العربي
                if self.contains_arabic(text):
                    # تطبيق المعالجة المتقدمة
                    processed_text = self.advanced_arabic_processing(text)
                    return self.original_functions['blf_draw'](fontid, processed_text)
                else:
                    return self.original_functions['blf_draw'](fontid, text)
            
            # استبدال الدالة
            blf.draw = enhanced_arabic_draw
            print("✅ تم تصحيح محرك رسم النصوص")
            
        except Exception as e:
            print(f"⚠️ خطأ في تصحيح محرك الرسم: {e}")
    
    def contains_arabic(self, text):
        """فحص وجود أحرف عربية في النص"""
        arabic_range = range(0x0600, 0x06FF + 1)
        return any(ord(char) in arabic_range for char in text)
    
    def advanced_arabic_processing(self, text):
        """معالجة متقدمة للنصوص العربية"""
        if not text:
            return text
        
        # خوارزمية ربط الحروف المحسنة
        arabic_letters = {
            'ا': {'isolated': 'ا', 'final': 'ـا'},
            'ب': {'isolated': 'ب', 'initial': 'بـ', 'medial': 'ـبـ', 'final': 'ـب'},
            'ت': {'isolated': 'ت', 'initial': 'تـ', 'medial': 'ـتـ', 'final': 'ـت'},
            'ث': {'isolated': 'ث', 'initial': 'ثـ', 'medial': 'ـثـ', 'final': 'ـث'},
            'ج': {'isolated': 'ج', 'initial': 'جـ', 'medial': 'ـجـ', 'final': 'ـج'},
            'ح': {'isolated': 'ح', 'initial': 'حـ', 'medial': 'ـحـ', 'final': 'ـح'},
            'خ': {'isolated': 'خ', 'initial': 'خـ', 'medial': 'ـخـ', 'final': 'ـخ'},
            'د': {'isolated': 'د', 'final': 'ـد'},
            'ذ': {'isolated': 'ذ', 'final': 'ـذ'},
            'ر': {'isolated': 'ر', 'final': 'ـر'},
            'ز': {'isolated': 'ز', 'final': 'ـز'},
            'س': {'isolated': 'س', 'initial': 'سـ', 'medial': 'ـسـ', 'final': 'ـس'},
            'ش': {'isolated': 'ش', 'initial': 'شـ', 'medial': 'ـشـ', 'final': 'ـش'},
            'ص': {'isolated': 'ص', 'initial': 'صـ', 'medial': 'ـصـ', 'final': 'ـص'},
            'ض': {'isolated': 'ض', 'initial': 'ضـ', 'medial': 'ـضـ', 'final': 'ـض'},
            'ط': {'isolated': 'ط', 'initial': 'طـ', 'medial': 'ـطـ', 'final': 'ـط'},
            'ظ': {'isolated': 'ظ', 'initial': 'ظـ', 'medial': 'ـظـ', 'final': 'ـظ'},
            'ع': {'isolated': 'ع', 'initial': 'عـ', 'medial': 'ـعـ', 'final': 'ـع'},
            'غ': {'isolated': 'غ', 'initial': 'غـ', 'medial': 'ـغـ', 'final': 'ـغ'},
            'ف': {'isolated': 'ف', 'initial': 'فـ', 'medial': 'ـفـ', 'final': 'ـف'},
            'ق': {'isolated': 'ق', 'initial': 'قـ', 'medial': 'ـقـ', 'final': 'ـق'},
            'ك': {'isolated': 'ك', 'initial': 'كـ', 'medial': 'ـكـ', 'final': 'ـك'},
            'ل': {'isolated': 'ل', 'initial': 'لـ', 'medial': 'ـلـ', 'final': 'ـل'},
            'م': {'isolated': 'م', 'initial': 'مـ', 'medial': 'ـمـ', 'final': 'ـم'},
            'ن': {'isolated': 'ن', 'initial': 'نـ', 'medial': 'ـنـ', 'final': 'ـن'},
            'ه': {'isolated': 'ه', 'initial': 'هـ', 'medial': 'ـهـ', 'final': 'ـه'},
            'و': {'isolated': 'و', 'final': 'ـو'},
            'ي': {'isolated': 'ي', 'initial': 'يـ', 'medial': 'ـيـ', 'final': 'ـي'},
            'ى': {'isolated': 'ى', 'final': 'ـى'},
            'ة': {'isolated': 'ة', 'final': 'ـة'},
        }
        
        # تطبيق ربط الحروف
        result = []
        chars = list(text)
        
        for i, char in enumerate(chars):
            if char in arabic_letters:
                forms = arabic_letters[char]
                
                # تحديد الشكل المناسب
                connects_before = i > 0 and chars[i-1] in arabic_letters and 'initial' in arabic_letters.get(chars[i-1], {})
                connects_after = i < len(chars)-1 and chars[i+1] in arabic_letters and 'final' in arabic_letters.get(chars[i+1], {})
                
                if connects_before and connects_after and 'medial' in forms:
                    result.append(forms['medial'])
                elif connects_before and 'final' in forms:
                    result.append(forms['final'])
                elif connects_after and 'initial' in forms:
                    result.append(forms['initial'])
                else:
                    result.append(forms['isolated'])
            else:
                result.append(char)
        
        # عكس النص للعرض من اليمين لليسار
        processed = ''.join(result)
        
        # فصل الكلمات العربية وعكسها
        words = processed.split(' ')
        arabic_words = []
        
        for word in words:
            if self.contains_arabic(word):
                arabic_words.append(word[::-1])  # عكس الكلمة العربية
            else:
                arabic_words.append(word)  # الاحتفاظ بالكلمات غير العربية
        
        return ' '.join(reversed(arabic_words))  # عكس ترتيب الكلمات
    
    def apply_system_patches(self):
        """تطبيق جميع التصحيحات"""
        print("🔧 بدء تطبيق التصحيحات على مستوى النظام...")
        
        # تصحيح إعدادات النظام
        self.patch_system_locale()
        
        # تصحيح نظام الواجهة
        self.patch_blender_ui_system()
        
        # تصحيح محرك الرسم
        self.patch_text_rendering_engine()
        
        # تطبيق إعدادات الخطوط
        self.apply_arabic_fonts()
        
        self.is_patched = True
        print("✅ تم تطبيق جميع التصحيحات بنجاح!")
    
    def apply_arabic_fonts(self):
        """تطبيق الخطوط العربية"""
        try:
            prefs = bpy.context.preferences
            
            # البحث عن أفضل خط عربي متاح
            arabic_fonts = [
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/arial.ttf", 
                "C:/Windows/Fonts/calibri.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            ]
            
            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    # تطبيق الخط على جميع عناصر الواجهة
                    if hasattr(prefs.view, 'font_path_ui'):
                        prefs.view.font_path_ui = font_path
                    
                    # تحسين إعدادات العرض
                    if hasattr(prefs.view, 'ui_scale'):
                        prefs.view.ui_scale = 1.15  # حجم أكبر قليلاً للوضوح
                    
                    if hasattr(prefs.view, 'use_international_fonts'):
                        prefs.view.use_international_fonts = True
                    
                    if hasattr(prefs.view, 'use_text_antialiasing'):
                        prefs.view.use_text_antialiasing = True
                    
                    print(f"✅ تم تطبيق الخط العربي: {font_path}")
                    break
                    
        except Exception as e:
            print(f"⚠️ خطأ في تطبيق الخطوط: {e}")

# إنشاء مثيل المصحح
system_patcher = SystemArabicPatcher()

class ARABIC_OT_apply_system_patch(bpy.types.Operator):
    """تطبيق التصحيح على مستوى النظام"""
    bl_idname = "arabic.apply_system_patch"
    bl_label = "تطبيق التصحيح الشامل"
    bl_description = "تطبيق تصحيح شامل للنصوص العربية على مستوى النظام"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            system_patcher.apply_system_patches()
            self.report({'INFO'}, "تم تطبيق التصحيح الشامل - أعد تشغيل Blender")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"خطأ في التطبيق: {str(e)}")
            return {'CANCELLED'}

class ARABIC_OT_test_system_arabic(bpy.types.Operator):
    """اختبار النظام العربي"""
    bl_idname = "arabic.test_system"
    bl_label = "اختبار النظام العربي"
    bl_description = "اختبار عمل النظام العربي المحسن"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        # اختبار الترجمات
        test_terms = ["File", "Edit", "Add", "Object", "Select", "Move", "Delete"]
        
        print("🧪 اختبار الترجمات:")
        for term in test_terms:
            if term in system_patcher.ui_translations:
                arabic = system_patcher.ui_translations[term]
                processed = system_patcher.advanced_arabic_processing(arabic)
                print(f"{term} → {arabic} → {processed}")
        
        self.report({'INFO'}, "تم اختبار النظام - راجع وحدة التحكم")
        return {'FINISHED'}

class ARABIC_PT_system_patch_panel(bpy.types.Panel):
    """لوحة التصحيح الشامل"""
    bl_label = "التصحيح الشامل للنظام"
    bl_idname = "ARABIC_PT_system_patch_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arabic Interface"
    
    def draw(self, context):
        layout = self.layout
        
        # حالة النظام
        box = layout.box()
        box.label(text="حالة النظام:")
        status = "مفعل ✅" if system_patcher.is_patched else "غير مفعل ⚠️"
        box.label(text=f"التصحيح: {status}")
        
        # أزرار التحكم
        layout.separator()
        layout.operator("arabic.apply_system_patch", text="تطبيق التصحيح الشامل")
        layout.operator("arabic.test_system", text="اختبار النظام العربي")
        
        # معلومات
        layout.separator()
        box = layout.box()
        box.label(text="ميزات التصحيح الشامل:", icon='INFO')
        box.label(text="• تصحيح محرك رسم النصوص")
        box.label(text="• ترجمة عناصر الواجهة")
        box.label(text="• ربط الحروف العربية")
        box.label(text="• عرض RTL صحيح")
        box.label(text="• خطوط عربية محسنة")

# قائمة الكلاسات
classes = [
    ARABIC_OT_apply_system_patch,
    ARABIC_OT_test_system_arabic,
    ARABIC_PT_system_patch_panel,
]

def register():
    """تسجيل البلجن"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    print("تم تفعيل التصحيح الشامل للنظام العربي")

def unregister():
    """إلغاء تسجيل البلجن"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    print("تم إلغاء تفعيل التصحيح الشامل")

if __name__ == "__main__":
    register()
