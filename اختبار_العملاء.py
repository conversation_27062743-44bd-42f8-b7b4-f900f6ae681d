#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة العملاء
"""

import sys
from PySide6.QtWidgets import QApplication
from database import Database
from customers_page import CustomersPage

def test_customers_page():
    """اختبار صفحة العملاء"""
    print("اختبار صفحة العملاء...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء اتصال قاعدة البيانات
        db = Database()
        
        # إنشاء صفحة العملاء
        customers_page = CustomersPage(db)
        
        # محاولة تحميل العملاء
        customers_page.load_customers()
        
        print("تم تحميل صفحة العملاء بنجاح!")
        print(f"عدد العملاء المحملين: {len(customers_page.customers)}")
        
        # إغلاق قاعدة البيانات
        db.close()
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار صفحة العملاء: {str(e)}")
        return False

if __name__ == "__main__":
    if test_customers_page():
        print("نجح اختبار صفحة العملاء!")
    else:
        print("فشل اختبار صفحة العملاء!")
