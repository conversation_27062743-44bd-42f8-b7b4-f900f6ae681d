#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار التحويل من الإنجليزية إلى العربية
يختبر جميع الاستعلامات والعمليات للتأكد من عمل النظام بشكل صحيح
"""

import sys
import traceback
from database import Database

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("اختبار الاتصال بقاعدة البيانات...")
    try:
        db = Database()
        print("تم الاتصال بقاعدة البيانات بنجاح")
        db.close()
        return True
    except Exception as e:
        print(f"فشل الاتصال بقاعدة البيانات: {str(e)}")
        return False

def test_tables_exist():
    """اختبار وجود الجداول العربية"""
    print("\nاختبار وجود الجداول العربية...")
    
    tables = [
        'الفئات', 'المنتجات', 'المخزون', 'الموردين', 'العملاء', 'الموظفين',
        'الفواتير', 'صفوف_الفواتير', 'دفعات_العملاء', 'فواتير_الموردين',
        'صفوف_فواتير_الموردين', 'دفعات_الموردين', 'البنوك', 'فروع_البنوك',
        'فئات_المصروفات', 'المصروفات', 'الصلاحيات', 'صلاحيات_الموظفين',
        'الحضور', 'الرواتب', 'العقود', 'الأقساط'
    ]
    
    try:
        db = Database()
        success_count = 0
        
        for table in tables:
            try:
                result = db.execute(f"SHOW TABLES LIKE '{table}'").fetchone()
                if result:
                    print(f"جدول '{table}' موجود")
                    success_count += 1
                else:
                    print(f"جدول '{table}' غير موجود")
            except Exception as e:
                print(f"خطأ في فحص جدول '{table}': {str(e)}")

        db.close()
        print(f"\nالنتيجة: {success_count}/{len(tables)} جدول موجود")
        return success_count == len(tables)
        
    except Exception as e:
        print(f"خطأ في اختبار الجداول: {str(e)}")
        return False

def test_basic_queries():
    """اختبار الاستعلامات الأساسية"""
    print("\nاختبار الاستعلامات الأساسية...")
    
    queries = [
        ("SELECT COUNT(*) FROM الفئات", "عدد الفئات"),
        ("SELECT COUNT(*) FROM المنتجات", "عدد المنتجات"),
        ("SELECT COUNT(*) FROM العملاء", "عدد العملاء"),
        ("SELECT COUNT(*) FROM الموظفين", "عدد الموظفين"),
        ("SELECT COUNT(*) FROM الفواتير", "عدد الفواتير"),
        ("SELECT COUNT(*) FROM الموردين", "عدد الموردين")
    ]
    
    try:
        db = Database()
        success_count = 0
        
        for query, description in queries:
            try:
                result = db.execute(query).fetchone()
                count = list(result.values())[0] if result else 0
                print(f"✅ {description}: {count}")
                success_count += 1
            except Exception as e:
                print(f"❌ خطأ في {description}: {str(e)}")
        
        db.close()
        print(f"\n📊 النتيجة: {success_count}/{len(queries)} استعلام نجح")
        return success_count == len(queries)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستعلامات: {str(e)}")
        return False

def test_foreign_keys():
    """اختبار المفاتيح الخارجية"""
    print("\n🔍 اختبار المفاتيح الخارجية...")
    
    try:
        db = Database()
        
        # اختبار العلاقة بين المنتجات والفئات
        try:
            result = db.execute("""
                SELECT p.الاسم, c.الاسم as اسم_الفئة 
                FROM المنتجات p 
                LEFT JOIN الفئات c ON p.الفئة_المعرف = c.المعرف 
                LIMIT 5
            """).fetchall()
            print(f"✅ العلاقة بين المنتجات والفئات تعمل بشكل صحيح ({len(result)} سجل)")
        except Exception as e:
            print(f"❌ خطأ في العلاقة بين المنتجات والفئات: {str(e)}")
        
        # اختبار العلاقة بين الفواتير والعملاء
        try:
            result = db.execute("""
                SELECT f.المعرف, c.الاسم as اسم_العميل 
                FROM الفواتير f 
                LEFT JOIN العملاء c ON f.العميل_المعرف = c.المعرف 
                LIMIT 5
            """).fetchall()
            print(f"✅ العلاقة بين الفواتير والعملاء تعمل بشكل صحيح ({len(result)} سجل)")
        except Exception as e:
            print(f"❌ خطأ في العلاقة بين الفواتير والعملاء: {str(e)}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المفاتيح الخارجية: {str(e)}")
        return False

def test_insert_operations():
    """اختبار عمليات الإدراج"""
    print("\n🔍 اختبار عمليات الإدراج...")
    
    try:
        db = Database()
        
        # اختبار إدراج فئة جديدة
        try:
            db.execute("""
                INSERT INTO الفئات (الاسم, الوصف) 
                VALUES ('فئة اختبار', 'فئة للاختبار فقط')
            """)
            db.commit()
            
            # التحقق من الإدراج
            result = db.execute("SELECT المعرف FROM الفئات WHERE الاسم = 'فئة اختبار'").fetchone()
            if result:
                category_id = result['المعرف']
                print("✅ إدراج فئة جديدة نجح")
                
                # حذف الفئة المختبرة
                db.execute("DELETE FROM الفئات WHERE المعرف = %s", (category_id,))
                db.commit()
                print("✅ حذف الفئة المختبرة نجح")
            else:
                print("❌ فشل في إدراج الفئة")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار إدراج الفئة: {str(e)}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات الإدراج: {str(e)}")
        return False

def test_update_operations():
    """اختبار عمليات التحديث"""
    print("\n🔍 اختبار عمليات التحديث...")
    
    try:
        db = Database()
        
        # إدراج سجل للاختبار
        db.execute("""
            INSERT INTO الفئات (الاسم, الوصف) 
            VALUES ('فئة للتحديث', 'وصف أولي')
        """)
        db.commit()
        
        # الحصول على معرف السجل
        result = db.execute("SELECT المعرف FROM الفئات WHERE الاسم = 'فئة للتحديث'").fetchone()
        if result:
            category_id = result['المعرف']
            
            # تحديث السجل
            db.execute("""
                UPDATE الفئات 
                SET الوصف = 'وصف محدث' 
                WHERE المعرف = %s
            """, (category_id,))
            db.commit()
            
            # التحقق من التحديث
            result = db.execute("SELECT الوصف FROM الفئات WHERE المعرف = %s", (category_id,)).fetchone()
            if result and result['الوصف'] == 'وصف محدث':
                print("✅ تحديث السجل نجح")
            else:
                print("❌ فشل في تحديث السجل")
            
            # حذف السجل المختبر
            db.execute("DELETE FROM الفئات WHERE المعرف = %s", (category_id,))
            db.commit()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات التحديث: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("بدء اختبار التحويل إلى اللغة العربية")
    print("=" * 50)
    
    tests = [
        ("اختبار الاتصال بقاعدة البيانات", test_database_connection),
        ("اختبار وجود الجداول", test_tables_exist),
        ("اختبار الاستعلامات الأساسية", test_basic_queries),
        ("اختبار المفاتيح الخارجية", test_foreign_keys),
        ("اختبار عمليات الإدراج", test_insert_operations),
        ("اختبار عمليات التحديث", test_update_operations)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"نتائج الاختبار النهائية:")
    print(f"نجح: {passed_tests}/{total_tests} اختبار")
    print(f"فشل: {total_tests - passed_tests}/{total_tests} اختبار")

    if passed_tests == total_tests:
        print("جميع الاختبارات نجحت! التحويل إلى العربية مكتمل بنجاح")
        return True
    else:
        print("بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
