-- ملف التحويل الشامل لقاعدة البيانات من الإنجليزية إلى العربية
-- يجب تنفيذ هذا الملف على قاعدة البيانات الحالية

-- تغيير اسم قاعدة البيانات
CREATE DATABASE IF NOT EXISTS نظام_المبيعات CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات الجديدة
USE نظام_المبيعات;

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- نسخ البيانات من قاعدة البيانات القديمة إلى الجديدة
-- نسخ جدول الفئات
CREATE TABLE الفئات AS SELECT * FROM sales_system.categories;
ALTER TABLE الفئات 
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN description الوصف TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول المنتجات
CREATE TABLE المنتجات AS SELECT * FROM sales_system.products;
ALTER TABLE المنتجات
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN category_id الفئة_المعرف INT,
CHANGE COLUMN barcode الباركود VARCHAR(100),
CHANGE COLUMN description الوصف TEXT,
CHANGE COLUMN unit الوحدة VARCHAR(50),
CHANGE COLUMN purchase_price سعر_الشراء DECIMAL(10,2),
CHANGE COLUMN selling_price سعر_البيع DECIMAL(10,2),
CHANGE COLUMN min_quantity الكمية_الدنيا INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN active نشط BOOLEAN,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول المخزون
CREATE TABLE المخزون AS SELECT * FROM sales_system.inventory;
ALTER TABLE المخزون
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN last_updated آخر_تحديث TIMESTAMP;

-- نسخ جدول الموردين
CREATE TABLE الموردين AS SELECT * FROM sales_system.suppliers;
ALTER TABLE الموردين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN active نشط BOOLEAN,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول العملاء
CREATE TABLE العملاء AS SELECT * FROM sales_system.customers;
ALTER TABLE العملاء
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- إضافة الحقول الجديدة للعملاء إذا لم تكن موجودة
ALTER TABLE العملاء 
ADD COLUMN IF NOT EXISTS إثبات_الشخصية VARCHAR(100) AFTER العنوان,
ADD COLUMN IF NOT EXISTS البنك VARCHAR(255) AFTER إثبات_الشخصية,
ADD COLUMN IF NOT EXISTS رقم_الحساب VARCHAR(100) AFTER البنك;

-- نسخ جدول الموظفين
CREATE TABLE الموظفين AS SELECT * FROM sales_system.employees;
ALTER TABLE الموظفين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- إضافة الحقول الجديدة للموظفين إذا لم تكن موجودة
ALTER TABLE الموظفين 
ADD COLUMN IF NOT EXISTS تاريخ_التوظيف DATE AFTER العنوان,
ADD COLUMN IF NOT EXISTS الراتب DECIMAL(10,2) DEFAULT 0 AFTER تاريخ_التوظيف,
ADD COLUMN IF NOT EXISTS الحالة ENUM('نشط', 'غير نشط') DEFAULT 'نشط' AFTER الراتب;

-- نسخ جدول الفواتير
CREATE TABLE الفواتير AS SELECT * FROM sales_system.invoices;
ALTER TABLE الفواتير
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN discount الخصم DECIMAL(10,2),
CHANGE COLUMN final_amount المبلغ_النهائي DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN payment_method طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي', 'تقسيط'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- إضافة حقل رقم الفاتورة إذا لم يكن موجوداً
ALTER TABLE الفواتير 
ADD COLUMN IF NOT EXISTS رقم_الفاتورة VARCHAR(50) AFTER المعرف;

-- نسخ جدول صفوف الفواتير
CREATE TABLE صفوف_الفواتير AS SELECT * FROM sales_system.invoice_items;
ALTER TABLE صفوف_الفواتير
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN unit_price سعر_الوحدة DECIMAL(10,2),
CHANGE COLUMN total_price إجمالي_السعر DECIMAL(10,2),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول دفعات العملاء
CREATE TABLE دفعات_العملاء AS SELECT * FROM sales_system.customer_payments;
ALTER TABLE دفعات_العملاء
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN payment_method طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول فواتير الموردين
CREATE TABLE فواتير_الموردين AS SELECT * FROM sales_system.supplier_invoices;
ALTER TABLE فواتير_الموردين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN supplier_id المورد_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول صفوف فواتير الموردين
CREATE TABLE صفوف_فواتير_الموردين AS SELECT * FROM sales_system.supplier_invoice_items;
ALTER TABLE صفوف_فواتير_الموردين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN price السعر DECIMAL(10,2),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول دفعات الموردين
CREATE TABLE دفعات_الموردين AS SELECT * FROM sales_system.supplier_payments;
ALTER TABLE دفعات_الموردين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN supplier_id المورد_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول البنوك إذا كان موجوداً
CREATE TABLE IF NOT EXISTS البنوك AS SELECT * FROM sales_system.banks;
ALTER TABLE البنوك
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول فروع البنوك إذا كان موجوداً
CREATE TABLE IF NOT EXISTS فروع_البنوك AS SELECT * FROM sales_system.bank_branches;
ALTER TABLE فروع_البنوك
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN main_bank_id البنك_الرئيسي_المعرف INT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN address العنوان VARCHAR(255),
CHANGE COLUMN account_number رقم_الحساب VARCHAR(50),
CHANGE COLUMN iban رقم_IBAN VARCHAR(50),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول فئات المصروفات إذا كان موجوداً
CREATE TABLE IF NOT EXISTS فئات_المصروفات AS SELECT * FROM sales_system.expense_categories;
ALTER TABLE فئات_المصروفات
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول المصروفات إذا كان موجوداً
CREATE TABLE IF NOT EXISTS المصروفات AS SELECT * FROM sales_system.expenses;
ALTER TABLE المصروفات
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN category_id الفئة_المعرف INT,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN description الوصف VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول الصلاحيات إذا كان موجوداً
CREATE TABLE IF NOT EXISTS الصلاحيات AS SELECT * FROM sales_system.permissions;
ALTER TABLE الصلاحيات
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN name الاسم VARCHAR(100),
CHANGE COLUMN description الوصف VARCHAR(255),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول صلاحيات الموظفين إذا كان موجوداً
CREATE TABLE IF NOT EXISTS صلاحيات_الموظفين AS SELECT * FROM sales_system.employee_permissions;
ALTER TABLE صلاحيات_الموظفين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN permission_id الصلاحية_المعرف INT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول الحضور إذا كان موجوداً
CREATE TABLE IF NOT EXISTS الحضور AS SELECT * FROM sales_system.attendance;
ALTER TABLE الحضور
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN check_in وقت_الدخول DATETIME,
CHANGE COLUMN check_out وقت_الخروج DATETIME,
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول الرواتب إذا كان موجوداً
CREATE TABLE IF NOT EXISTS الرواتب AS SELECT * FROM sales_system.salaries;
ALTER TABLE الرواتب
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN month الشهر TINYINT,
CHANGE COLUMN year السنة SMALLINT,
CHANGE COLUMN base_salary الراتب_الأساسي DECIMAL(10,2),
CHANGE COLUMN commission العمولة DECIMAL(10,2),
CHANGE COLUMN deductions الخصومات DECIMAL(10,2),
CHANGE COLUMN additions الإضافات DECIMAL(10,2),
CHANGE COLUMN payment_date تاريخ_الدفع DATETIME,
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- نسخ جدول العقود إذا كان موجوداً
CREATE TABLE IF NOT EXISTS العقود AS SELECT * FROM sales_system.contracts;
ALTER TABLE العقود
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN contract_number رقم_العقد VARCHAR(100),
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN bank_branch_id فرع_البنك_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN installment_amount مبلغ_القسط DECIMAL(10,2),
CHANGE COLUMN number_of_installments عدد_الأقساط INT,
CHANGE COLUMN start_date تاريخ_البداية DATE,
CHANGE COLUMN end_date تاريخ_النهاية DATE,
CHANGE COLUMN status الحالة ENUM('نشط', 'منتهي', 'ملغي'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- نسخ جدول الأقساط إذا كان موجوداً
CREATE TABLE IF NOT EXISTS الأقساط AS SELECT * FROM sales_system.installments;
ALTER TABLE الأقساط
CHANGE COLUMN id المعرف INT AUTO_INCREMENT PRIMARY KEY,
CHANGE COLUMN contract_id العقد_المعرف INT,
CHANGE COLUMN installment_number رقم_القسط INT,
CHANGE COLUMN due_date تاريخ_الاستحقاق DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN payment_date تاريخ_الدفع DATE,
CHANGE COLUMN status الحالة ENUM('مستحق', 'مدفوع', 'متأخر'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- إعادة إنشاء المفاتيح الخارجية بالأسماء العربية
-- مفاتيح جدول المنتجات
ALTER TABLE المنتجات
ADD CONSTRAINT FK_المنتجات_الفئة FOREIGN KEY (الفئة_المعرف) REFERENCES الفئات (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول المخزون
ALTER TABLE المخزون
ADD CONSTRAINT FK_المخزون_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الفواتير
ALTER TABLE الفواتير
ADD CONSTRAINT FK_الفواتير_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_الفواتير_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صفوف_الفواتير
ALTER TABLE صفوف_الفواتير
ADD CONSTRAINT FK_صفوف_الفواتير_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صفوف_الفواتير_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول دفعات_العملاء
ALTER TABLE دفعات_العملاء
ADD CONSTRAINT FK_دفعات_العملاء_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_دفعات_العملاء_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول فواتير_الموردين
ALTER TABLE فواتير_الموردين
ADD CONSTRAINT FK_فواتير_الموردين_المورد FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صفوف_فواتير_الموردين
ALTER TABLE صفوف_فواتير_الموردين
ADD CONSTRAINT FK_صفوف_فواتير_الموردين_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صفوف_فواتير_الموردين_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول دفعات_الموردين
ALTER TABLE دفعات_الموردين
ADD CONSTRAINT FK_دفعات_الموردين_المورد FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_دفعات_الموردين_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول فروع_البنوك
ALTER TABLE فروع_البنوك
ADD CONSTRAINT FK_فروع_البنوك_البنك_الرئيسي FOREIGN KEY (البنك_الرئيسي_المعرف) REFERENCES البنوك (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول المصروفات
ALTER TABLE المصروفات
ADD CONSTRAINT FK_المصروفات_الفئة FOREIGN KEY (الفئة_المعرف) REFERENCES فئات_المصروفات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صلاحيات_الموظفين
ALTER TABLE صلاحيات_الموظفين
ADD CONSTRAINT FK_صلاحيات_الموظفين_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صلاحيات_الموظفين_الصلاحية FOREIGN KEY (الصلاحية_المعرف) REFERENCES الصلاحيات (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الحضور
ALTER TABLE الحضور
ADD CONSTRAINT FK_الحضور_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الرواتب
ALTER TABLE الرواتب
ADD CONSTRAINT FK_الرواتب_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول العقود
ALTER TABLE العقود
ADD CONSTRAINT FK_العقود_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_العقود_فرع_البنك FOREIGN KEY (فرع_البنك_المعرف) REFERENCES فروع_البنوك (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_العقود_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول الأقساط
ALTER TABLE الأقساط
ADD CONSTRAINT FK_الأقساط_العقد FOREIGN KEY (العقد_المعرف) REFERENCES العقود (المعرف) ON DELETE CASCADE;

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- تم الانتهاء من التحويل الشامل لقاعدة البيانات إلى اللغة العربية
SELECT 'تم تحويل قاعدة البيانات إلى اللغة العربية بنجاح' AS النتيجة;
