from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                               QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                               QComboBox, QMessageBox, QTabWidget, QDialog,
                               QFormLayout)
from PySide6.QtCore import Qt
from database import Database

class AddBankDialog(QDialog):
    """نافذة حوار إضافة/تعديل بنك"""
    
    def __init__(self, parent=None, bank_data=None, is_branch=False, main_bank_id=None):
        super().__init__(parent)
        self.bank_data = bank_data or {}  # تهيئة كقاموس فارغ إذا كان None
        self.is_branch = is_branch
        self.main_bank_id = main_bank_id
        self.db = Database()
        self.setup_ui()
        
        if bank_data:
            self.load_bank_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(self.get_title())
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        
        # اسم البنك
        self.name_input = QLineEdit()
        form_layout.addRow("الاسم:", self.name_input)
        
        # العنوان (للفرع فقط)
        if self.is_branch:
            self.address_input = QLineEdit()
            form_layout.addRow("العنوان:", self.address_input)
            
            # رقم الحساب
            self.account_input = QLineEdit()
            form_layout.addRow("رقم الحساب:", self.account_input)
            
            # رقم IBAN
            self.iban_input = QLineEdit()
            form_layout.addRow("رقم IBAN:", self.iban_input)
        
        # ملاحظات
        self.notes_input = QLineEdit()
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)
        
    def get_title(self):
        """الحصول على عنوان النافذة المناسب"""
        if not self.bank_data:
            return "إضافة بنك جديد" if not self.is_branch else "إضافة فرع جديد"
        else:
            return "تعديل بيانات البنك" if not self.is_branch else "تعديل بيانات الفرع"
        
    def load_bank_data(self):
        """تحميل بيانات البنك للتعديل"""
        if not self.bank_data:
            return
            
        self.name_input.setText(self.bank_data.get('name', ''))
        self.notes_input.setText(self.bank_data.get('notes', ''))
        
        if self.is_branch:
            if hasattr(self, 'address_input'):
                self.address_input.setText(self.bank_data.get('address', ''))
            if hasattr(self, 'account_input'):
                self.account_input.setText(self.bank_data.get('account_number', ''))
            if hasattr(self, 'iban_input'):
                self.iban_input.setText(self.bank_data.get('iban', ''))
        
    def get_bank_data(self):
        """الحصول على بيانات البنك المدخلة"""
        data = {
            'name': self.name_input.text(),
            'notes': self.notes_input.text()
        }
        
        if self.is_branch:
            branch_data = {
                'address': self.address_input.text() if hasattr(self, 'address_input') else '',
                'account_number': self.account_input.text() if hasattr(self, 'account_input') else '',
                'iban': self.iban_input.text() if hasattr(self, 'iban_input') else '',
                'main_bank_id': self.main_bank_id
            }
            data.update(branch_data)
            
        return data
        
    def accept(self):
        """التحقق من البيانات قبل الحفظ"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم البنك", QMessageBox.StandardButton.Ok)
            return
            
        super().accept()


class BanksPage(QWidget):
    """صفحة إدارة البنوك
    تتيح إدارة البنوك الرئيسية والفروع وحسابات الشركة
    """
    def __init__(self, db=None):
        super().__init__()
        self.db = db if db is not None else Database()  # تهيئة قاعدة البيانات إذا لم يتم تمريرها
        self.bank_ids = {}  # تخزين معرفات البنوك للاستخدام لاحقاً
        self.setup_ui()
        self.filter_banks()  # عرض البيانات الأولية
        self.update_bank_filter()  # تحديث قائمة البنوك في فلتر الفروع
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # إضافة تبويبات رئيسية
        self.banks_tab = QWidget()
        self.branches_tab = QWidget()
        
        self.setup_banks_tab()
        self.setup_branches_tab()
        
        self.tabs.addTab(self.banks_tab, "البنوك الرئيسية")
        self.tabs.addTab(self.branches_tab, "الفروع")
        
        main_layout.addWidget(self.tabs)
        
    def setup_banks_tab(self):
        """إعداد تبويب البنوك الرئيسية"""
        layout = QVBoxLayout(self.banks_tab)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        self.bank_search_input = QLineEdit()
        self.bank_search_input.setPlaceholderText("بحث...")
        self.bank_search_input.setMaximumWidth(200)
        self.bank_search_input.textChanged.connect(self.filter_banks)
        
        search_layout.addWidget(self.bank_search_input)
        search_layout.addStretch()
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        self.add_bank_btn = QPushButton("إضافة بنك")
        self.edit_bank_btn = QPushButton("تعديل")
        self.delete_bank_btn = QPushButton("حذف")
        
        self.add_bank_btn.clicked.connect(self.add_bank)
        self.edit_bank_btn.clicked.connect(self.edit_bank)
        self.delete_bank_btn.clicked.connect(self.delete_bank)
        
        actions_layout.addWidget(self.add_bank_btn)
        actions_layout.addWidget(self.edit_bank_btn)
        actions_layout.addWidget(self.delete_bank_btn)
        actions_layout.addStretch()
        
        # جدول البنوك
        self.banks_table = QTableWidget()
        self.banks_table.setColumnCount(3)
        self.banks_table.setHorizontalHeaderLabels([
            "رقم", "اسم البنك", "ملاحظات"
        ])
        
        layout.addLayout(search_layout)
        layout.addLayout(actions_layout)
        layout.addWidget(self.banks_table)
        
    def setup_branches_tab(self):
        """إعداد تبويب الفروع"""
        layout = QVBoxLayout(self.branches_tab)
        
        # شريط البحث والفلترة
        filter_layout = QHBoxLayout()
        
        self.branch_search_input = QLineEdit()
        self.branch_search_input.setPlaceholderText("بحث...")
        self.branch_search_input.setMaximumWidth(200)
        self.branch_search_input.textChanged.connect(self.filter_branches)
        
        self.bank_filter = QComboBox()
        self.bank_filter.addItem("كل البنوك")
        self.bank_filter.currentTextChanged.connect(self.filter_branches)
        
        filter_layout.addWidget(self.branch_search_input)
        filter_layout.addWidget(QLabel("البنك:"))
        filter_layout.addWidget(self.bank_filter)
        filter_layout.addStretch()
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        self.add_branch_btn = QPushButton("إضافة فرع")
        self.edit_branch_btn = QPushButton("تعديل")
        self.delete_branch_btn = QPushButton("حذف")
        
        self.add_branch_btn.clicked.connect(self.add_branch)
        self.edit_branch_btn.clicked.connect(self.edit_branch)
        self.delete_branch_btn.clicked.connect(self.delete_branch)
        
        actions_layout.addWidget(self.add_branch_btn)
        actions_layout.addWidget(self.edit_branch_btn)
        actions_layout.addWidget(self.delete_branch_btn)
        actions_layout.addStretch()
        
        # جدول الفروع
        self.branches_table = QTableWidget()
        self.branches_table.setColumnCount(6)
        self.branches_table.setHorizontalHeaderLabels([
            "رقم", "البنك الرئيسي", "اسم الفرع", "العنوان", "رقم الحساب", "ملاحظات"
        ])
        
        layout.addLayout(filter_layout)
        layout.addLayout(actions_layout)
        layout.addWidget(self.branches_table)
        
    def filter_banks(self):
        """تصفية قائمة البنوك"""
        search_text = self.bank_search_input.text().strip()
        
        query = "SELECT المعرف, الاسم, ملاحظات FROM البنوك WHERE 1=1"
        params = []

        if search_text:
            query += " AND (الاسم LIKE %s OR ملاحظات LIKE %s)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern])
            
        query += " ORDER BY name"
        
        try:
            self.db.execute(query, params)
            banks = self.db.fetchall()
            self.banks_table.setRowCount(len(banks))
            
            for row, bank in enumerate(banks):
                for col, value in enumerate(bank.values()):
                    item = QTableWidgetItem(str(value or ""))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.banks_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات البنوك: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def update_bank_filter(self):
        """تحديث قائمة البنوك في فلتر الفروع"""
        try:
            cursor = self.db.execute("SELECT المعرف, الاسم FROM البنوك ORDER BY الاسم")
            banks = cursor.fetchall()
            current_text = self.bank_filter.currentText()

            self.bank_filter.clear()
            self.bank_filter.addItem("كل البنوك")

            self.bank_ids = {}  # تخزين معرفات البنوك للاستخدام لاحقاً

            for bank in banks:
                self.bank_filter.addItem(bank['الاسم'])
                self.bank_ids[bank['الاسم']] = bank['المعرف']
                
            index = self.bank_filter.findText(current_text)
            if index >= 0:
                self.bank_filter.setCurrentIndex(index)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل قائمة البنوك: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def filter_branches(self):
        """تصفية قائمة الفروع"""
        search_text = self.branch_search_input.text().strip()
        bank_filter = self.bank_filter.currentText()
        
        query = """
            SELECT b.id, m.name as main_bank, b.name, b.address, b.account_number, b.notes
            FROM bank_branches b
            JOIN banks m ON b.main_bank_id = m.id
            WHERE 1=1
        """
        params = []
        
        if bank_filter != "كل البنوك":
            query += " AND m.name = %s"
            params.append(bank_filter)
            
        if search_text:
            query += " AND (b.name LIKE %s OR b.address LIKE %s OR b.account_number LIKE %s OR b.notes LIKE %s)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
            
        query += " ORDER BY m.name, b.name"
        
        try:
            cursor = self.db.execute(query, params)
            branches = cursor.fetchall()
            self.branches_table.setRowCount(len(branches))
            
            for row, branch in enumerate(branches):
                for col, key in enumerate(['id', 'main_bank', 'name', 'address', 'account_number', 'notes']):
                    value = branch.get(key, '')
                    item = QTableWidgetItem(str(value or ""))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.branches_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفروع: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def add_bank(self):
        """إضافة بنك جديد"""
        dialog = AddBankDialog(self)
        if dialog.exec():
            bank_data = dialog.get_bank_data()
            try:
                self.db.execute(
                    "INSERT INTO البنوك (الاسم, ملاحظات) VALUES (%s, %s)",
                    (bank_data['name'], bank_data['notes'])
                )
                self.db.commit()
                self.filter_banks()
                self.update_bank_filter()
                QMessageBox.information(self, "نجاح", "تم إضافة البنك بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة البنك: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def edit_bank(self):
        """تعديل بيانات بنك"""
        current_row = self.banks_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار بنك للتعديل", QMessageBox.StandardButton.Ok)
            return
            
        bank_id_item = self.banks_table.item(current_row, 0)
        if not bank_id_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد البنك", QMessageBox.StandardButton.Ok)
            return
            
        bank_id = int(bank_id_item.text())
        
        try:
            cursor = self.db.execute(
                "SELECT الاسم, ملاحظات FROM البنوك WHERE المعرف = %s",
                (bank_id,)
            )
            bank = cursor.fetchone()
            
            if not bank:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات البنك", QMessageBox.StandardButton.Ok)
                return
                
            dialog = AddBankDialog(self, {
                'name': bank['الاسم'],
                'notes': bank['ملاحظات']
            })

            if dialog.exec():
                updated_data = dialog.get_bank_data()

                self.db.execute(
                    "UPDATE البنوك SET الاسم = %s, ملاحظات = %s WHERE المعرف = %s",
                    (updated_data['name'], updated_data['notes'], bank_id)
                )
                self.db.commit()
                self.filter_banks()
                self.update_bank_filter()
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات البنك بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل البنك: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def delete_bank(self):
        """حذف بنك"""
        current_row = self.banks_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار بنك للحذف", QMessageBox.StandardButton.Ok)
            return
            
        bank_id_item = self.banks_table.item(current_row, 0)
        bank_name_item = self.banks_table.item(current_row, 1)
        
        if not bank_id_item or not bank_name_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد البنك", QMessageBox.StandardButton.Ok)
            return
            
        bank_id = int(bank_id_item.text())
        bank_name = bank_name_item.text()
        
        # التحقق من وجود فروع مرتبطة
        self.db.execute(
            "SELECT COUNT(*) as count FROM فروع_البنوك WHERE البنك_الرئيسي_المعرف = %s",
            (bank_id,)
        )
        result = self.db.fetchone()
        branches_count = result['count'] if result else 0
        
        if branches_count > 0:
            QMessageBox.warning(
                self, "تنبيه",
                f"لا يمكن حذف البنك لأنه يحتوي على {branches_count} فرع. يجب حذف الفروع أولاً.",
                QMessageBox.StandardButton.Ok
            )
            return
            
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف البنك {bank_name}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.db.execute("DELETE FROM البنوك WHERE المعرف = %s", (bank_id,))
                self.db.commit()
                self.filter_banks()
                self.update_bank_filter()
                QMessageBox.information(self, "نجاح", "تم حذف البنك بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف البنك: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def add_branch(self):
        """إضافة فرع جديد"""
        if self.bank_filter.count() <= 1:
            QMessageBox.warning(self, "تنبيه", "يجب إضافة بنك رئيسي أولاً", QMessageBox.StandardButton.Ok)
            return
            
        # اختيار البنك الرئيسي
        bank_name = self.bank_filter.currentText()
        if bank_name == "كل البنوك":
            bank_name = self.bank_filter.itemText(1)  # أول بنك في القائمة
            
        bank_id = self.bank_ids.get(bank_name)
        
        dialog = AddBankDialog(self, is_branch=True, main_bank_id=bank_id)
        if dialog.exec():
            branch_data = dialog.get_bank_data()
            try:
                self.db.execute(
                    """
                    INSERT INTO فروع_البنوك
                    (البنك_الرئيسي_المعرف, الاسم, العنوان, رقم_الحساب, رقم_IBAN, ملاحظات)
                    VALUES (%s, %s, %s, ?, ?, ?)
                    """,
                    (
                        branch_data['main_bank_id'],
                        branch_data['name'],
                        branch_data['address'],
                        branch_data['account_number'],
                        branch_data['iban'],
                        branch_data['notes']
                    )
                )
                self.db.commit()
                self.filter_branches()
                QMessageBox.information(self, "نجاح", "تم إضافة الفرع بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الفرع: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def edit_branch(self):
        """تعديل بيانات فرع"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار فرع للتعديل", QMessageBox.StandardButton.Ok)
            return
            
        branch_id_item = self.branches_table.item(current_row, 0)
        if not branch_id_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد الفرع", QMessageBox.StandardButton.Ok)
            return
            
        branch_id = int(branch_id_item.text())
        
        try:
            cursor = self.db.execute(
                """
                SELECT b.main_bank_id, b.name, b.address, b.account_number, b.iban, b.notes
                FROM bank_branches b
                WHERE b.id = %s
                """,
                (branch_id,)
            )
            branch = cursor.fetchone()
            
            if not branch:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات الفرع", QMessageBox.StandardButton.Ok)
                return
                
            dialog = AddBankDialog(self, {
                'name': branch['name'],
                'address': branch['address'],
                'account_number': branch['account_number'],
                'iban': branch['iban'],
                'notes': branch['notes']
            }, is_branch=True, main_bank_id=branch['main_bank_id'])
            
            if dialog.exec():
                updated_data = dialog.get_bank_data()
                
                self.db.execute(
                    """
                    UPDATE bank_branches
                    SET name = %s, address = %s, account_number = %s, iban = %s, notes = %s
                    WHERE id = %s
                    """,
                    (
                        updated_data['name'],
                        updated_data['address'],
                        updated_data['account_number'],
                        updated_data['iban'],
                        updated_data['notes'],
                        branch_id
                    )
                )
                self.db.commit()
                self.filter_branches()
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات الفرع بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الفرع: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def delete_branch(self):
        """حذف فرع"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار فرع للحذف", QMessageBox.StandardButton.Ok)
            return
            
        branch_id_item = self.branches_table.item(current_row, 0)
        branch_name_item = self.branches_table.item(current_row, 2)
        
        if not branch_id_item or not branch_name_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد الفرع", QMessageBox.StandardButton.Ok)
            return
            
        branch_id = int(branch_id_item.text())
        branch_name = branch_name_item.text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الفرع {branch_name}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.db.execute("DELETE FROM فروع_البنوك WHERE المعرف = %s", (branch_id,))
                self.db.commit()
                self.filter_branches()
                QMessageBox.information(self, "نجاح", "تم حذف الفرع بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الفرع: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def fix_mysql_compatibility(self):
        """تحديث الكود ليتوافق مع MySQL بدلاً من SQLite"""
        # تم إضافة هذه الدالة لتغيير التعليمات البرمجية للتوافق مع قاعدة بيانات MySQL
        pass 