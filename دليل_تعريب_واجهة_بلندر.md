# دليل تعريب واجهة Blender الشامل

## نظرة عامة
هذا الدليل يوضح كيفية تعريب واجهة Blender بالكامل، بما في ذلك القوائم والأزرار والنوافذ، مع دعم الكتابة من اليمين إلى اليسار (RTL) والخطوط العربية.

## المشكلة
واجهة Blender الافتراضية لا تدعم:
- عرض النصوص العربية بشكل صحيح
- الكتابة من اليمين إلى اليسار (RTL)
- الخطوط العربية المناسبة
- ترجمة عناصر الواجهة

## الحل المقترح
تم تطوير بلجنين متكاملين:

### 1. بلجن تعريب الواجهة (`blender_arabic_interface.py`)
**الوظائف الأساسية:**
- ترجمة أكثر من 200 عنصر من عناصر الواجهة
- إنشاء ملفات الترجمة العربية (.po)
- تطبيق الخطوط العربية
- تكوين دعم RTL

### 2. مخصص الواجهة العربية (`arabic_ui_customizer.py`)
**الوظائف المتقدمة:**
- تحكم دقيق في حجم وشكل الخطوط
- إعدادات الاتجاه والتخطيط
- سمات ألوان مخصصة للنصوص العربية
- اكتشاف تلقائي للخطوط العربية

## التثبيت والإعداد

### الخطوة 1: تثبيت البلجنين
```python
# في وحدة تحكم Python في Blender
import sys
sys.path.append("مسار_مجلد_البلجنز")

# تثبيت بلجن التعريب الأساسي
import blender_arabic_interface
blender_arabic_interface.register()

# تثبيت مخصص الواجهة
import arabic_ui_customizer
arabic_ui_customizer.register()
```

### الخطوة 2: الوصول للبلجنين
1. اضغط `N` لفتح الشريط الجانبي في 3D Viewport
2. ستجد تبويب "Arabic Interface" 
3. يحتوي على لوحتين:
   - **تعريب واجهة Blender**: للترجمة الأساسية
   - **مخصص الواجهة العربية**: للتخصيص المتقدم

## استخدام بلجن التعريب الأساسي

### الميزات المتاحة:
- **تطبيق الترجمة العربية**: ترجمة عناصر الواجهة
- **إنشاء ملف الترجمة**: إنشاء ملف .po للترجمة
- **تثبيت الدعم الكامل**: تكوين شامل للدعم العربي

### طريقة الاستخدام:
1. انقر "تطبيق الترجمة العربية"
2. انقر "إنشاء ملف الترجمة" 
3. انقر "تثبيت الدعم الكامل"
4. أعد تشغيل Blender لرؤية التغييرات

## استخدام مخصص الواجهة المتقدم

### إعدادات الخط:
- **استخدام خط عربي**: تفعيل/إلغاء الخط العربي
- **حجم الخط**: تحكم في حجم النصوص (0.5 - 3.0)
- **مسار الخط**: تحديد ملف خط مخصص
- **اكتشاف الخطوط**: البحث التلقائي عن الخطوط العربية

### إعدادات الاتجاه:
- **تخطيط من اليمين لليسار**: تفعيل RTL
- **عكس اللوحات**: عكس ترتيب اللوحات (تجريبي)

### إعدادات المظهر:
- **سمة عربية**: تطبيق ألوان مناسبة للنصوص العربية
- **لون التمييز**: تخصيص لون العناصر المحددة

### إعدادات النصوص:
- **تباين النص**: تحسين وضوح النصوص (0.1 - 2.0)
- **ظلال النصوص**: إضافة ظلال للوضوح

## الخطوط العربية المدعومة

### Windows:
- Arial (C:/Windows/Fonts/arial.ttf)
- Tahoma (C:/Windows/Fonts/tahoma.ttf) 
- Calibri (C:/Windows/Fonts/calibri.ttf)
- Times New Roman (C:/Windows/Fonts/times.ttf)

### macOS:
- Arial (/System/Library/Fonts/Arial.ttf)
- Helvetica (/System/Library/Fonts/Helvetica.ttc)

### Linux:
- DejaVu Sans (/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf)
- Liberation Sans (/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf)

## العناصر المترجمة

### القوائم الرئيسية:
- File → ملف
- Edit → تحرير
- Add → إضافة
- Object → كائن
- Mesh → شبكة

### أوضاع العمل:
- Object Mode → وضع الكائن
- Edit Mode → وضع التحرير
- Sculpt Mode → وضع النحت

### النوافذ:
- 3D Viewport → منفذ ثلاثي الأبعاد
- Properties → خصائص
- Outliner → المخطط
- Timeline → الخط الزمني

### الأدوات:
- Move → نقل
- Rotate → دوران
- Scale → تكبير
- Select → تحديد
- Delete → حذف

## الإعدادات المُوصى بها

### للحصول على أفضل تجربة عربية:
```
حجم الخط: 1.2
الخط المُوصى به: Tahoma أو Arial
تباين النص: 1.2
تفعيل الخطوط الدولية: نعم
تفعيل تنعيم النصوص: نعم
```

## حل المشاكل الشائعة

### المشكلة: النصوص لا تظهر بالعربية
**الحل:**
1. تأكد من تفعيل "الخطوط الدولية"
2. أعد تشغيل Blender
3. تحقق من مسار الخط العربي

### المشكلة: النصوص صغيرة جداً
**الحل:**
1. زد حجم الخط إلى 1.2 أو أكثر
2. زد تباين النص إلى 1.2
3. استخدم خط Tahoma بدلاً من Arial

### المشكلة: الواجهة لا تدعم RTL
**الحل:**
1. هذه ميزة تجريبية تحتاج تطوير إضافي
2. حالياً يمكن تحسين عرض النصوص فقط
3. RTL الكامل يتطلب تعديل كود Blender الأساسي

## القيود الحالية

### ما يعمل:
✅ ترجمة النصوص الأساسية
✅ تطبيق الخطوط العربية
✅ تحسين وضوح النصوص
✅ إعدادات مخصصة للواجهة

### ما لا يعمل بالكامل:
❌ RTL كامل للقوائم والنوافذ
❌ ترجمة جميع النصوص الديناميكية
❌ عكس ترتيب اللوحات
❌ دعم التشكيل العربي في الواجهة

## التطوير المستقبلي

### المخطط للإصدارات القادمة:
1. **دعم RTL كامل**: تطوير نظام RTL شامل
2. **ترجمة شاملة**: ترجمة جميع عناصر الواجهة
3. **خطوط مدمجة**: إضافة خطوط عربية مدمجة
4. **سمات عربية**: سمات ألوان مصممة للعربية
5. **دعم اللهجات**: دعم المصطلحات المحلية

## المساهمة في التطوير

### كيفية المساهمة:
1. **الترجمة**: إضافة ترجمات جديدة لقاموس ARABIC_TRANSLATIONS
2. **الاختبار**: تجربة البلجن على أنظمة مختلفة
3. **التطوير**: تحسين الكود وإضافة ميزات جديدة
4. **التوثيق**: تحسين الدليل وإضافة أمثلة

### بنية الكود:
```
blender_arabic_interface.py
├── ARABIC_TRANSLATIONS (قاموس الترجمات)
├── ARABIC_OT_apply_translation (تطبيق الترجمة)
├── ARABIC_OT_create_translation_file (إنشاء ملف .po)
└── ARABIC_PT_interface_panel (واجهة المستخدم)

arabic_ui_customizer.py
├── ArabicUIProperties (خصائص التخصيص)
├── ARABIC_OT_apply_ui_settings (تطبيق الإعدادات)
├── ARABIC_OT_detect_arabic_fonts (اكتشاف الخطوط)
└── ARABIC_PT_ui_customizer_panel (لوحة التحكم)
```

## الخلاصة
هذا الحل يوفر تعريباً جزئياً لواجهة Blender مع تحسينات كبيرة في عرض النصوص العربية. رغم أنه لا يحقق RTL كاملاً، إلا أنه يحسن تجربة المستخدم العربي بشكل ملحوظ.

للحصول على تعريب كامل، نحتاج لتعديل كود Blender الأساسي أو انتظار دعم رسمي من فريق التطوير.

---
**تم تطوير هذا الحل لخدمة المجتمع العربي في مجال التصميم ثلاثي الأبعاد** 🎨✨

**ملاحظة**: هذا مشروع مفتوح المصدر ونرحب بالمساهمات من المطورين العرب لتحسينه وتطويره.
