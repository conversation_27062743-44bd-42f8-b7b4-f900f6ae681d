from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                               QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                               QComboBox, QDateEdit, QDoubleSpinBox, QMessageBox,
                               QDialog, QInputDialog)
from PySide6.QtCore import Qt, QDate
from database import Database

class ExpenseDialog(QDialog):
    """نافذة حوار إضافة/تعديل مصروف"""
    
    def __init__(self, parent=None, expense_data=None):
        super().__init__(parent)
        self.expense_data = expense_data
        
        # الحصول على اتصال قاعدة البيانات من النافذة الرئيسية
        if parent and hasattr(parent, 'db'):
            self.db = parent.db
        else:
            from database import Database
            self.db = Database()
            
        self.setup_ui()
        
        if expense_data:
            self.load_expense_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة مصروف جديد" if not self.expense_data else "تعديل مصروف")
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        
        # التاريخ
        date_layout = QHBoxLayout()
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        date_layout.addWidget(QLabel("التاريخ:"))
        date_layout.addWidget(self.date_input)
        
        # الفئة
        category_layout = QHBoxLayout()
        self.category_input = QComboBox()
        self.category_input.setEditable(True)
        self.load_categories()
        category_layout.addWidget(QLabel("الفئة:"))
        category_layout.addWidget(self.category_input)
        
        # المبلغ
        amount_layout = QHBoxLayout()
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 1000000)
        self.amount_input.setSingleStep(10)
        amount_layout.addWidget(QLabel("المبلغ:"))
        amount_layout.addWidget(self.amount_input)
        
        # الوصف
        description_layout = QHBoxLayout()
        self.description_input = QLineEdit()
        description_layout.addWidget(QLabel("الوصف:"))
        description_layout.addWidget(self.description_input)
        
        # ملاحظات
        notes_layout = QHBoxLayout()
        self.notes_input = QLineEdit()
        notes_layout.addWidget(QLabel("ملاحظات:"))
        notes_layout.addWidget(self.notes_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        # إضافة كل العناصر للتخطيط الرئيسي
        layout.addLayout(date_layout)
        layout.addLayout(category_layout)
        layout.addLayout(amount_layout)
        layout.addLayout(description_layout)
        layout.addLayout(notes_layout)
        layout.addLayout(buttons_layout)
        
        # تحديث العناصر إذا كانت للتعديل
        if self.expense_data:
            self.load_expense_data()
        
    def load_categories(self):
        """تحميل فئات المصروفات"""
        if not self.category_input:
            return
            
        try:
            self.db.execute("SELECT الاسم FROM فئات_المصروفات ORDER BY الاسم")
            categories = self.db.fetchall() or []

            self.category_input.clear()
            for category in categories:
                if category:
                    self.category_input.addItem(category['الاسم'])
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فئات المصروفات: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        if not self.expense_data:
            return
            
        if not all([
            self.date_input,
            self.category_input,
            self.amount_input,
            self.description_input,
            self.notes_input
        ]):
            return
            
        try:
            # تحميل التاريخ
            date_str = self.expense_data.get('date', '')
            if date_str:
                date = QDate.fromString(date_str, 'yyyy-MM-dd')
                if date.isValid():
                    self.date_input.setDate(date)
                    
            # تحميل الفئة
            category = self.expense_data.get('category', '')
            if category:
                index = self.category_input.findText(category)
                if index >= 0:
                    self.category_input.setCurrentIndex(index)
                else:
                    self.category_input.addItem(category)
                    self.category_input.setCurrentText(category)
                    
            # تحميل المبلغ
            amount = self.expense_data.get('amount', 0)
            self.amount_input.setValue(float(amount))
            
            # تحميل الوصف والملاحظات
            self.description_input.setText(self.expense_data.get('description', ''))
            self.notes_input.setText(self.expense_data.get('notes', ''))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المصروف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def get_expense_data(self):
        """الحصول على بيانات المصروف المدخلة"""
        try:
            if not all([
                self.date_input,
                self.category_input,
                self.amount_input,
                self.description_input
            ]):
                return None
                
            return {
                'date': self.date_input.date().toString('yyyy-MM-dd'),
                'category': self.category_input.currentText().strip(),
                'amount': self.amount_input.value(),
                'description': self.description_input.text().strip(),
                'notes': self.notes_input.text().strip() if self.notes_input else ''
            }
        except Exception:
            return None
        
    def accept(self):
        """التحقق من البيانات قبل الحفظ"""
        if not all([
            self.category_input,
            self.amount_input,
            self.description_input
        ]):
            QMessageBox.warning(self, "خطأ", "حدث خطأ في تحميل عناصر النافذة", QMessageBox.StandardButton.Ok)
            return
            
        try:
            category = self.category_input.currentText().strip()
            if not category:
                QMessageBox.warning(self, "خطأ", "يجب إدخال فئة المصروف", QMessageBox.StandardButton.Ok)
                return
                
            amount = self.amount_input.value()
            if amount <= 0:
                QMessageBox.warning(self, "خطأ", "يجب إدخال مبلغ أكبر من صفر", QMessageBox.StandardButton.Ok)
                return
                
            description = self.description_input.text().strip()
            if not description:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف للمصروف", QMessageBox.StandardButton.Ok)
                return
                
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من البيانات: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def reject(self):
        """إلغاء العملية"""
        super().reject()


class ExpensesPage(QWidget):
    """صفحة إدارة المصروفات
    تتيح إدارة مصروفات الشركة وتصنيفها وتتبعها
    """
    def __init__(self, db=None):
        super().__init__()
        if not db:
            from database import Database
            self.db = Database()
        else:
            self.db = db
        self.setup_ui()
        self.load_expenses()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.setMaximumWidth(200)
        self.search_input.textChanged.connect(self.filter_expenses)
        
        self.category_filter = QComboBox()
        self.category_filter.addItem("كل الفئات")
        self.load_categories()
        self.category_filter.currentTextChanged.connect(self.filter_expenses)
        
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.dateChanged.connect(self.filter_expenses)
        
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.dateChanged.connect(self.filter_expenses)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(QLabel("الفئة:"))
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(QLabel("من:"))
        search_layout.addWidget(self.date_from)
        search_layout.addWidget(QLabel("إلى:"))
        search_layout.addWidget(self.date_to)
        search_layout.addStretch()
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("إضافة مصروف")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.add_category_btn = QPushButton("إضافة فئة")
        
        self.add_btn.clicked.connect(self.add_expense)
        self.edit_btn.clicked.connect(self.edit_expense)
        self.delete_btn.clicked.connect(self.delete_expense)
        self.add_category_btn.clicked.connect(self.add_category)
        
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addWidget(self.add_category_btn)
        actions_layout.addStretch()
        
        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels([
            "رقم", "التاريخ", "الفئة", "المبلغ", "الوصف", "ملاحظات"
        ])
        
        # إحصائيات
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("إجمالي المصروفات: 0")
        stats_layout.addWidget(self.total_label)
        stats_layout.addStretch()
        
        main_layout.addLayout(search_layout)
        main_layout.addLayout(actions_layout)
        main_layout.addWidget(self.expenses_table)
        main_layout.addLayout(stats_layout)
        
    def load_categories(self):
        """تحميل فئات المصروفات"""
        if not self.category_filter:
            return
            
        try:
            self.db.execute("SELECT الاسم FROM فئات_المصروفات ORDER BY الاسم")
            categories = self.db.fetchall() or []

            current_text = self.category_filter.currentText()
            self.category_filter.clear()
            self.category_filter.addItem("كل الفئات")

            for category in categories:
                if category:
                    self.category_filter.addItem(category['الاسم'])
                    
            if current_text:
                index = self.category_filter.findText(current_text)
                if index >= 0:
                    self.category_filter.setCurrentIndex(index)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فئات المصروفات: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def load_expenses(self):
        """تحميل بيانات المصروفات"""
        self.filter_expenses()
        
    def filter_expenses(self):
        """تصفية قائمة المصروفات"""
        try:
            search_text = self.search_input.text().strip() if self.search_input else ""
            category = self.category_filter.currentText() if self.category_filter else "كل الفئات"
            date_from = self.date_from.date().toString("yyyy-MM-dd") if self.date_from else QDate.currentDate().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd") if self.date_to else QDate.currentDate().toString("yyyy-MM-dd")
            
            # بناء الاستعلام
            query = """
                SELECT e.id, e.date, c.name as category, e.amount,
                    e.description, e.notes
                FROM expenses e
                JOIN expense_categories c ON c.id = e.category_id
                WHERE e.date BETWEEN ? AND ?
            """
            params = [date_from, date_to]
            
            if search_text:
                query += " AND (e.description LIKE %s OR e.notes LIKE %s)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
                
            if category != "كل الفئات":
                query += " AND c.name = %s"
                params.append(category)
                
            query += " ORDER BY e.date DESC"
            
            # تنفيذ الاستعلام
            self.db.execute(query, params)
            expenses = self.db.fetchall() or []
            
            # تحديث الجدول
            if not self.expenses_table:
                return
                
            self.expenses_table.setRowCount(len(expenses))
            total_amount = 0
            
            for row, expense in enumerate(expenses):
                if not expense:
                    continue
                
                self.expenses_table.setItem(row, 0, QTableWidgetItem(str(expense.get('id', ''))))
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense.get('date', '')))
                self.expenses_table.setItem(row, 2, QTableWidgetItem(expense.get('category', '')))
                self.expenses_table.setItem(row, 3, QTableWidgetItem(str(expense.get('amount', 0))))
                self.expenses_table.setItem(row, 4, QTableWidgetItem(expense.get('description', '')))
                self.expenses_table.setItem(row, 5, QTableWidgetItem(expense.get('notes', '')))
                
                total_amount += expense.get('amount', 0)
            
            # تحديث إجمالي المصروفات
            if self.total_label:
                self.total_label.setText(f"إجمالي المصروفات: {total_amount:,.2f}")
                
                # تحديث إحصائيات الفئات
                if category == "كل الفئات":
                    self.db.execute("""
                        SELECT c.name, COUNT(*) as count, SUM(e.amount) as total
                        FROM expenses e
                        JOIN expense_categories c ON c.id = e.category_id
                        WHERE e.date BETWEEN ? AND ?
                        GROUP BY c.name
                        ORDER BY total DESC
                    """, [date_from, date_to])
                    
                    category_stats = self.db.fetchall() or []
                    if category_stats:
                        stats = []
                        for stat in category_stats:
                            stats.append(
                                f"{stat.get('name', '')}: "
                                f"{stat.get('count', 0)} مصروف - "
                                f"{stat.get('total', 0):,.2f} ريال"
                            )
                        
                        self.total_label.setToolTip(f"تفاصيل المصروفات حسب الفئة:\n" + "\n".join(stats))
                        return
                        
                self.total_label.setToolTip("")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل المصروفات: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            dialog = ExpenseDialog(self)
            if dialog.exec():
                expense_data = dialog.get_expense_data()
                if not expense_data:
                    raise ValueError("لم يتم الحصول على بيانات المصروف")
                    
                # التحقق من وجود الفئة أو إضافتها
                category = expense_data.get('category', '').strip()
                if not category:
                    raise ValueError("يجب تحديد فئة المصروف")
                    
                # التحقق من وجود الفئة أو إضافتها
                self.db.execute(
                    "SELECT المعرف FROM فئات_المصروفات WHERE الاسم = %s",
                    (category,)
                )
                category_row = self.db.fetchone()

                if not category_row:
                    self.db.execute(
                        "INSERT INTO فئات_المصروفات (الاسم) VALUES (%s)",
                        (category,)
                    )
                    category_id = self.db.lastrowid
                else:
                    category_id = category_row['المعرف']

                # إضافة المصروف
                self.db.execute("""
                    INSERT INTO المصروفات (
                        التاريخ, الفئة_المعرف, المبلغ, الوصف, ملاحظات
                    ) VALUES (%s, %s, %s, %s, %s)
                """, (
                    expense_data.get('date', ''),
                    category_id,
                    expense_data.get('amount', 0),
                    expense_data.get('description', ''),
                    expense_data.get('notes', '')
                ))
                
                self.db.commit()
                self.load_categories()
                self.filter_expenses()
                QMessageBox.information(self, "نجاح", "تم إضافة المصروف بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المصروف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def edit_expense(self):
        """تعديل مصروف"""
        if not self.expenses_table:
            return
            
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مصروف للتعديل", QMessageBox.StandardButton.Ok)
            return
            
        try:
            # الحصول على معرف المصروف
            id_item = self.expenses_table.item(current_row, 0)
            if not id_item:
                QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد المصروف", QMessageBox.StandardButton.Ok)
                return
                
            expense_id = int(id_item.text())
            
            # تحميل بيانات المصروف
            cursor = self.db.execute("""
                SELECT e.*, c.name as category
                FROM expenses e
                JOIN expense_categories c ON c.id = e.category_id
                WHERE e.id = %s
            """, (expense_id,))
            
            if not cursor:
                QMessageBox.critical(self, "خطأ", "حدث خطأ في قراءة بيانات المصروف", QMessageBox.StandardButton.Ok)
                return
                
            expense = cursor.fetchone()
            if not expense:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المصروف", QMessageBox.StandardButton.Ok)
                return
                
            # تحويل النتيجة إلى قاموس
            expense_dict = dict(zip([
                'id', 'date', 'category_id', 'amount',
                'description', 'notes', 'category'
            ], expense))
            
            # فتح نافذة التعديل
            dialog = ExpenseDialog(self, expense_dict)
            if dialog.exec():
                updated_data = dialog.get_expense_data()
                if not updated_data:
                    QMessageBox.critical(self, "خطأ", "لم يتم الحصول على البيانات المحدثة", QMessageBox.StandardButton.Ok)
                    return
                    
                # التحقق من وجود الفئة أو إضافتها
                category = updated_data.get('category', '').strip()
                if not category:
                    QMessageBox.warning(self, "خطأ", "يجب تحديد فئة المصروف", QMessageBox.StandardButton.Ok)
                    return
                    
                cursor = self.db.execute(
                    "SELECT المعرف FROM فئات_المصروفات WHERE الاسم = %s",
                    (category,)
                )

                if not cursor:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ في قراءة بيانات الفئة", QMessageBox.StandardButton.Ok)
                    return

                category_row = cursor.fetchone()
                if not category_row:
                    cursor = self.db.execute(
                        "INSERT INTO فئات_المصروفات (الاسم) VALUES (?)",
                        (category,)
                    )
                    if not cursor:
                        QMessageBox.critical(self, "خطأ", "حدث خطأ في إضافة الفئة", QMessageBox.StandardButton.Ok)
                        return

                    category_id = cursor.lastrowid
                else:
                    category_id = category_row['المعرف']
                
                # تحديث المصروف
                cursor = self.db.execute("""
                    UPDATE expenses
                    SET date = %s, category_id = %s, amount = %s,
                        description = %s, notes = %s
                    WHERE id = %s
                """, (
                    updated_data.get('date', ''),
                    category_id,
                    updated_data.get('amount', 0),
                    updated_data.get('description', ''),
                    updated_data.get('notes', ''),
                    expense_id
                ))
                
                if not cursor:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ في تحديث المصروف", QMessageBox.StandardButton.Ok)
                    return
                    
                self.db.commit()
                self.filter_expenses()
                QMessageBox.information(self, "نجاح", "تم تحديث المصروف بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المصروف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def delete_expense(self):
        """حذف مصروف"""
        if not self.expenses_table:
            return
            
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مصروف للحذف", QMessageBox.StandardButton.Ok)
            return
            
        try:
            # الحصول على معرف المصروف
            id_item = self.expenses_table.item(current_row, 0)
            if not id_item:
                QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد المصروف", QMessageBox.StandardButton.Ok)
                return
                
            expense_id = int(id_item.text())
            
            # التأكيد قبل الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا المصروف؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                cursor = self.db.execute("DELETE FROM المصروفات WHERE المعرف = %s", (expense_id,))
                if not cursor:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ في حذف المصروف", QMessageBox.StandardButton.Ok)
                    return
                    
                self.db.commit()
                self.filter_expenses()
                QMessageBox.information(self, "نجاح", "تم حذف المصروف بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المصروف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def add_category(self):
        """إضافة فئة مصروفات جديدة"""
        category, ok = QInputDialog.getText(self, "إضافة فئة", "اسم الفئة:")
        
        if ok and category.strip():
            try:
                self.db.execute(
                    "INSERT INTO فئات_المصروفات (الاسم) VALUES (?)",
                    (category.strip(),)
                )
                self.db.commit()
                self.load_categories()
                QMessageBox.information(self, "نجاح", "تم إضافة الفئة بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الفئة: {str(e)}", QMessageBox.StandardButton.Ok) 