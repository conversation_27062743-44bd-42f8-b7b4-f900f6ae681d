#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مخصص الواجهة العربية لـ Blender
يوفر تحكماً كاملاً في مظهر الواجهة العربية
"""

bl_info = {
    "name": "مخصص الواجهة العربية",
    "author": "مطور عربي",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "User Preferences > Interface",
    "description": "تخصيص شامل للواجهة العربية مع دعم RTL والخطوط",
    "category": "Interface",
}

import bpy
import os
from bpy.props import BoolProperty, StringProperty, EnumProperty, FloatProperty
from bpy.types import Panel, Operator, PropertyGroup

# خصائص التخصيص العربي
class ArabicUIProperties(PropertyGroup):
    # إعدادات الخط
    font_size: FloatProperty(
        name="حجم الخط",
        description="حجم خط الواجهة",
        default=1.0,
        min=0.5,
        max=3.0,
        step=0.1
    )
    
    use_arabic_font: BoolProperty(
        name="استخدام خط عربي",
        description="تطبيق خط عربي على الواجهة",
        default=True
    )
    
    font_path: StringProperty(
        name="مسار الخط",
        description="مسار ملف الخط العربي",
        default="",
        subtype='FILE_PATH'
    )
    
    # إعدادات الاتجاه
    use_rtl_layout: BoolProperty(
        name="تخطيط من اليمين لليسار",
        description="تطبيق تخطيط RTL على الواجهة",
        default=True
    )
    
    mirror_panels: BoolProperty(
        name="عكس اللوحات",
        description="عكس ترتيب اللوحات والقوائم",
        default=False
    )
    
    # إعدادات الألوان
    use_arabic_theme: BoolProperty(
        name="سمة عربية",
        description="تطبيق سمة ألوان مناسبة للواجهة العربية",
        default=False
    )
    
    accent_color: FloatProperty(
        name="لون التمييز",
        description="لون التمييز للعناصر المحددة",
        default=0.2,
        min=0.0,
        max=1.0,
        step=0.1
    )
    
    # إعدادات النصوص
    text_contrast: FloatProperty(
        name="تباين النص",
        description="مستوى تباين النصوص",
        default=1.0,
        min=0.1,
        max=2.0,
        step=0.1
    )
    
    use_text_shadows: BoolProperty(
        name="ظلال النصوص",
        description="إضافة ظلال للنصوص لتحسين الوضوح",
        default=False
    )

class ARABIC_OT_apply_ui_settings(Operator):
    """تطبيق إعدادات الواجهة العربية"""
    bl_idname = "arabic.apply_ui_settings"
    bl_label = "تطبيق الإعدادات"
    bl_description = "تطبيق جميع إعدادات الواجهة العربية"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        props = context.scene.arabic_ui_props
        prefs = context.preferences
        
        try:
            # تطبيق إعدادات الخط
            if props.use_arabic_font:
                self.apply_font_settings(props, prefs)
            
            # تطبيق إعدادات الاتجاه
            if props.use_rtl_layout:
                self.apply_rtl_settings(props, prefs)
            
            # تطبيق السمة العربية
            if props.use_arabic_theme:
                self.apply_arabic_theme(props, prefs)
            
            # تطبيق إعدادات النصوص
            self.apply_text_settings(props, prefs)
            
            self.report({'INFO'}, "تم تطبيق إعدادات الواجهة العربية")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"خطأ في تطبيق الإعدادات: {str(e)}")
            return {'CANCELLED'}
    
    def apply_font_settings(self, props, prefs):
        """تطبيق إعدادات الخط"""
        # تعيين حجم الخط
        if hasattr(prefs.view, 'ui_scale'):
            prefs.view.ui_scale = props.font_size
        
        # تطبيق الخط العربي
        if props.font_path and os.path.exists(props.font_path):
            if hasattr(prefs.view, 'font_path_ui'):
                prefs.view.font_path_ui = props.font_path
        
        # تفعيل الخطوط الدولية
        if hasattr(prefs.view, 'use_international_fonts'):
            prefs.view.use_international_fonts = True
        
        print("تم تطبيق إعدادات الخط")
    
    def apply_rtl_settings(self, props, prefs):
        """تطبيق إعدادات الاتجاه RTL"""
        # هذه إعدادات تجريبية لمحاكاة RTL
        # في التطبيق الحقيقي نحتاج تعديل كود Blender الأساسي
        
        if props.mirror_panels:
            # محاولة عكس ترتيب اللوحات
            self.mirror_ui_panels()
        
        print("تم تطبيق إعدادات RTL")
    
    def apply_arabic_theme(self, props, prefs):
        """تطبيق السمة العربية"""
        theme = prefs.themes[0]
        
        # تعديل ألوان الواجهة لتناسب النصوص العربية
        if hasattr(theme, 'user_interface'):
            ui = theme.user_interface
            
            # تحسين تباين الألوان
            if hasattr(ui, 'wcol_regular'):
                # لون الخلفية
                ui.wcol_regular.inner = (0.2, 0.2, 0.25, 1.0)
                # لون النص
                ui.wcol_regular.text = (0.9, 0.9, 0.9, 1.0)
                # لون التمييز
                ui.wcol_regular.item = (props.accent_color, 0.4, 0.6, 1.0)
        
        print("تم تطبيق السمة العربية")
    
    def apply_text_settings(self, props, prefs):
        """تطبيق إعدادات النصوص"""
        # تطبيق تباين النص
        if hasattr(prefs.view, 'ui_scale'):
            # تعديل حجم الخط حسب التباين
            adjusted_scale = props.font_size * props.text_contrast
            prefs.view.ui_scale = min(adjusted_scale, 3.0)
        
        # تفعيل تنعيم النصوص
        if hasattr(prefs.view, 'use_text_antialiasing'):
            prefs.view.use_text_antialiasing = True
        
        print("تم تطبيق إعدادات النصوص")
    
    def mirror_ui_panels(self):
        """عكس ترتيب اللوحات (تجريبي)"""
        # هذه دالة تجريبية لمحاكاة عكس اللوحات
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                for region in area.regions:
                    if region.type == 'UI':
                        # محاولة تغيير موضع الشريط الجانبي
                        # هذا يتطلب تعديل كود Blender الأساسي
                        pass

class ARABIC_OT_reset_ui_settings(Operator):
    """إعادة تعيين إعدادات الواجهة"""
    bl_idname = "arabic.reset_ui_settings"
    bl_label = "إعادة التعيين"
    bl_description = "إعادة تعيين جميع إعدادات الواجهة للقيم الافتراضية"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        props = context.scene.arabic_ui_props
        prefs = context.preferences
        
        # إعادة تعيين الخصائص
        props.font_size = 1.0
        props.use_arabic_font = True
        props.font_path = ""
        props.use_rtl_layout = True
        props.mirror_panels = False
        props.use_arabic_theme = False
        props.accent_color = 0.2
        props.text_contrast = 1.0
        props.use_text_shadows = False
        
        # إعادة تعيين إعدادات Blender
        if hasattr(prefs.view, 'ui_scale'):
            prefs.view.ui_scale = 1.0
        
        self.report({'INFO'}, "تم إعادة تعيين إعدادات الواجهة")
        return {'FINISHED'}

class ARABIC_OT_detect_arabic_fonts(Operator):
    """اكتشاف الخطوط العربية المتاحة"""
    bl_idname = "arabic.detect_fonts"
    bl_label = "اكتشاف الخطوط العربية"
    bl_description = "البحث عن الخطوط العربية المتاحة في النظام"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        fonts = self.find_arabic_fonts()
        
        if fonts:
            # عرض الخطوط المكتشفة
            message = "الخطوط العربية المتاحة:\n" + "\n".join(fonts[:5])
            self.report({'INFO'}, message)
            
            # تعيين أول خط متاح
            if fonts:
                context.scene.arabic_ui_props.font_path = fonts[0]
        else:
            self.report({'WARNING'}, "لم يتم العثور على خطوط عربية")
        
        return {'FINISHED'}
    
    def find_arabic_fonts(self):
        """البحث عن الخطوط العربية في النظام"""
        import platform
        
        font_paths = []
        system = platform.system()
        
        if system == "Windows":
            font_dirs = ["C:/Windows/Fonts/"]
            font_names = ["arial.ttf", "tahoma.ttf", "calibri.ttf", "times.ttf"]
        elif system == "Darwin":  # macOS
            font_dirs = ["/System/Library/Fonts/", "/Library/Fonts/"]
            font_names = ["Arial.ttf", "Helvetica.ttc", "Times.ttc"]
        else:  # Linux
            font_dirs = ["/usr/share/fonts/", "/usr/local/share/fonts/"]
            font_names = ["DejaVuSans.ttf", "LiberationSans-Regular.ttf"]
        
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                for font_name in font_names:
                    font_path = os.path.join(font_dir, font_name)
                    if os.path.exists(font_path):
                        font_paths.append(font_path)
        
        return font_paths

class ARABIC_PT_ui_customizer_panel(Panel):
    """لوحة مخصص الواجهة العربية"""
    bl_label = "مخصص الواجهة العربية"
    bl_idname = "ARABIC_PT_ui_customizer_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arabic Interface"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.arabic_ui_props
        
        # إعدادات الخط
        box = layout.box()
        box.label(text="إعدادات الخط:", icon='FONT_DATA')
        box.prop(props, "use_arabic_font")
        if props.use_arabic_font:
            box.prop(props, "font_size")
            box.prop(props, "font_path")
            box.operator("arabic.detect_fonts", text="اكتشاف الخطوط")
        
        # إعدادات الاتجاه
        box = layout.box()
        box.label(text="إعدادات الاتجاه:", icon='ORIENTATION_GLOBAL')
        box.prop(props, "use_rtl_layout")
        if props.use_rtl_layout:
            box.prop(props, "mirror_panels")
        
        # إعدادات المظهر
        box = layout.box()
        box.label(text="إعدادات المظهر:", icon='COLOR')
        box.prop(props, "use_arabic_theme")
        if props.use_arabic_theme:
            box.prop(props, "accent_color")
        
        # إعدادات النصوص
        box = layout.box()
        box.label(text="إعدادات النصوص:", icon='TEXT')
        box.prop(props, "text_contrast")
        box.prop(props, "use_text_shadows")
        
        # أزرار التحكم
        layout.separator()
        row = layout.row(align=True)
        row.operator("arabic.apply_ui_settings", text="تطبيق الإعدادات")
        row.operator("arabic.reset_ui_settings", text="إعادة التعيين")
        
        # معلومات الحالة
        layout.separator()
        box = layout.box()
        box.label(text="معلومات الحالة:")
        
        prefs = context.preferences
        if hasattr(prefs.view, 'ui_scale'):
            box.label(text=f"حجم الخط الحالي: {prefs.view.ui_scale:.1f}")
        
        if hasattr(prefs.view, 'use_international_fonts'):
            status = "مفعل" if prefs.view.use_international_fonts else "غير مفعل"
            box.label(text=f"الخطوط الدولية: {status}")

# قائمة الكلاسات للتسجيل
classes = [
    ArabicUIProperties,
    ARABIC_OT_apply_ui_settings,
    ARABIC_OT_reset_ui_settings,
    ARABIC_OT_detect_arabic_fonts,
    ARABIC_PT_ui_customizer_panel,
]

def register():
    """تسجيل البلجن"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # إضافة الخصائص للمشهد
    bpy.types.Scene.arabic_ui_props = bpy.props.PointerProperty(type=ArabicUIProperties)
    
    print("تم تفعيل مخصص الواجهة العربية")

def unregister():
    """إلغاء تسجيل البلجن"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    # حذف الخصائص
    del bpy.types.Scene.arabic_ui_props
    
    print("تم إلغاء تفعيل مخصص الواجهة العربية")

if __name__ == "__main__":
    register()
