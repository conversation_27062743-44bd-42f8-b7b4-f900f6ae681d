باستخدام بايثون  و PySide6و css

استخدم قاعدة بيانات mysql
host = "localhost" 
user_r = "root" 
password_r = "kh123456"

قم بإنشاء تطبيق سطح مكتب RTL يدعم العربية بالكامل  من اليمين إلى اليسار ويكون في ملف واحد مع إضافة التعلقات لكل الدوال والكلاسات بالعربي فوق كل دالة وكلاس 
 بناء على البيانات ونشاء الجداول وعلاقات قوية وربط بينها في قاعدة البيانات في ملف db.py اسم قاعدة البيانات Sales_system.

الشريط العلوي (tool Bar  Top Bar)  يحتوي على قوائم ملف معلومات حماية اختصارات مساعدة تخصيص

اريد التطبيق متجاوب وتسايل يتماشى مع شاشة ال720*1280 فما فوق واخذ هذا في الاعتبار في كل الحقول والنوافذ بحيث لا يحدث تداخل 


القائمة الجانبية الرأسية (Side Nav Bar)

عرض ثابت ~150px، ممتد رأسياً بكامل الارتفاع، بلون خلفي ازرق داكن (#). قابلة للطي ..


تحتوي على أيقونات فقط (من أعلى إلى أسفل): "الشاشة الرئيسية" (بيت) – "نقطة بيع" – "العملاء" – "المخزن" – "الموظفين" – "المصروفات" – "الدوين والاقساط" – "الموردين"– "البنوك"– "الفواتير"– "العقود"– "تقارير مالية"– اعدادات .


 واليسار العلوي شريط افقي الصف الأول فلاتر وبحث بطاقات إحصائية وفلاتر متقدمة.. 

تحته شريط افقي لازرار للإجراءات مثل إضافة حذف تعديل طباعة وغيرها من الاجرائات لكل قسم حسب القسم

ثم باقي المساحة الجزء الايسر السفلي لعرض البيانات للعملاء جداول وواجهات  وغيرها  كل تفاصيل المعاملة حسب جداول قاعدة بيانات مع ترقيم تلقائي لصفوف الجداول وجعل كل البيانات في منتصف الحقول لجميع أنواع الأدوات مثل الليبول واللاين ايديت وحقول التاريخ وصفوف الجداول وغيرها 


كل إجراء (Button) عبارة عن بطاقة مربعة بحجم ~ 80×80 ، )، مع أيقونة كبيرة في المنتصف (حجم الأيقونة ~40px) ونص مختصر أسفلها.

خطوط تلوينية رفيعة أسفل كل بطاقة لتمييز الفئة: حذف  (أحمر)، طباعة  (رمادي)، تقارير مالية (أزرق)، حالة  (أصفر)، مصروفات  (برتقالي)، إدارة  (بنفسجي)،  إضافة  (أخضر داكن).


قسم الفلترة والبحث في الأعلى


عرض الواجهات والجداول  من اليمين الى اليسار.



قاعدة البيانات تحتوي على الجداول الرئيسية التالية:

نقصة البيع : 
اختيار المنتجات والبحث عنها عن طريق الاسم التجاري او الاسم العام او الفئة او الباركود 
وادراجها الى السلة مع عرض سعر البيع والكمية المتوفر والصلاحية اذا كان منتج خاضع لصلاحية والتعبئة قطعة او صندوق او غيرها في قائمة على يمين النافذة 

إمكانية تحديد الكمية والعدد بجانبة خانه البحث لادراج الكمية تلقائيا عند إضافة المنتج للسله او الفاتورة .

الجزء الايسر يعرض الفاتورة او السلة مع ترقيم فواتير تلقائي وخيارات البيع مثل نفدي اجل أقساط وفي حال اختيار اجل او أقساط يظهر خيار لاختيار اسم العميل او البحث عنه او إضافة زبون جديد .. تحديد المدفوع الفعلي وخيار لتحديد الخصم .. وتاريخ الفاتورة وخانه للبحث عن فاتورة سابقة ..

العملاء: 
*اسم العميل واللقب *رقم الهاتف *العنوان  اثبات شخصي البنك الفرع رقم الحساب الوكيل  من طرف  رقم العقد .. يتم استخدام بعض هذه البيانات للمبيعات الآجلة او اقساط على المصرف لربطها بعقد واقساط ان احتجت

الموردين: 
اضافة موردين رقم المورد (يقترح تلقائيا) اسم المورد العنوان رقم الهاتف تاريخ الاضافة  والحالة نشط او غير نشط .

ربط الفواتير بالموردين عند شراء منتجات وادراجها للمخازن للمنتجات والاسعار مع عرض حسابات المالية للموردين رصيدهم الحالي حسب الفواتير المسددة 

ويمكن البحث عن اي فاتورة للمورد عرض الفواتير والمنتجات المرتبطة بالمورد 

المخازن :
امكانية اضافة او تعديل او حذف  فواتير منتجات 
اختيار المورد او اضافة جديد  -رقم الفاتورو- تاريخ الشراء - اسم الصنف -الوحدة - سعر الشراء - سعر البيع - سعر البيع اقساط - الكمية الحالية اذا كان الصنف موجود سابقا - الكمية المضافة - العدد الكلي للكمية اذا كان الصنف خاضع للصلاحية يتم ادراجها - اذا كان المنتج موجود سابقا يجب اخذ هذا في الاعتبار مع عرض الباركود واسم المنتج واخر سعر شراء - وعرض كل الاسماء والاصناف المرتبطة بنفس الباركود عند اضافة باركود او اسم منتج 

بطاقة صنف لمنتجات الفواتير والمخازن:
 تحديد الفئة للاصناف الاسم العام - ادراج الاسم التجاري للصنف اذا كان غير موجود سابقا يتم اقتراح باركود من 4 ارقام حسب التسلسل مع امكانية ادخال باركود يدوي او مسحه ضوئا بقارء الباركود والتحقق من عدم وجود باركود مطابق لصنف اخر قبل تخصيص الباركود للمنتج .
امكانية تقسيم المنتجات لفئات واضافة اصناف فرعية للصنف الرئيسي مع امكانية الحذف 

الفلاتر والبحث المتقدم مثل حسب المورد او الفاتور او التاريخ وغيرها 

االموظفين:
ادارة شاملة للموظفين الرواتب والحضور والانصراف و الرصيد الحالي و الخصم واضافة النسب وغيرها  وخيارات تسجيل الدخول والصلاحيات ان اردت اضافته كمستخدم مع اسم المستخدم وكلمة مرور خاصة بالموظفين الذين يتم ادراجهم كمستخدمين للتطبيق 

المصروفات : 

إدارة المصروفات والتصنيف والتاريخ والوصف والمبلغ والتقارير وغيرها 


البنوك

اضافة بنوك رئيسية  وامكانية اضافة بنوك فرعية لكل بنك رئيسي مع ارقام حسابات الشركة لكل فرع حتى يتم ادراجة تلقائيا عند انشاء عقود بيع اقساط بين الشركة والعملاء لبيع فاتورة بالاقساط المصرفية 


العقود: اضافة وتعديل وابرام عقود بين الشركة والعملاء لنظام اقساط شهرية يتم خصمها من البنك للعميل لصالح حساب الشركة بمبلغ متفق عليه شهريا حسب الفاتور الخاصة بالعميل .

الاقساط :
عرض عقود وفواتير العملاء لمتابعة اقسطاهم بدقة وادراج المبالغ والشهور المسددة حسب كشف الحساب والاقساط المتأخرة وكل مايتعلق بهذا الجانب من بيانات 

تقارير مالية:
تقارير مالية شاملة لكل قسم والارباح والخسائر وغيرها وانضمة متقدمة للبحث والفلترة ..


تحليل المتطلبات التقنية
لغة البرمجة: Python
واجهة المستخدم: PySide6 مع CSS
قاعدة البيانات: MySQL
Host: localhost
User: root
Password: kh123456

اسم قاعدة البيانات: Sales_system

هيكل واجهة المستخدم
تطبيق RTL يدعم العربية بالكامل
متجاوب مع شاشات 1280×720 فما فوق
يتكون من:
شريط علوي (Toolbar)
قائمة جانبية رأسية (~150px) قابلة للطي
منطقة رئيسية تحتوي على:
شريط أفقي للفلاتر والبحث
شريط أزرار للإجراءات
منطقة عرض البيانات

الوحدات الرئيسية للتطبيق
نقطة البيع (POS)
إدارة العملاء
إدارة المخزون
إدارة الموظفين
إدارة المصروفات
إدارة الديون والأقساط
إدارة الموردين
إدارة البنوك
إدارة الفواتير
إدارة العقود
التقارير المالية
الإعدادات


تفاصيل الوحدات الرئيسية
نقطة البيع: واجهة بيع تتيح البحث عن المنتجات وإضافتها للسلة مع خيارات دفع متعددة
العملاء: إدارة بيانات العملاء ومعلوماتهم الشخصية والمالية
الموردين: إدارة الموردين وربط الفواتير بهم وتتبع الحسابات
المخازن: إدارة المخزون والمنتجات وفواتير الشراء
الموظفين: إدارة شاملة للموظفين والرواتب والحضور والصلاحيات
المصروفات: تسجيل وتصنيف وإدارة المصروفات
البنوك: إدارة البنوك الرئيسية والفرعية وحسابات الشركة
العقود: إدارة عقود البيع بالأقساط
الأقساط: متابعة أقساط العملاء والمدفوعات
التقارير المالية: تقارير شاملة للأرباح والخسائر وأنظمة بحث متقدمة


متطلبات خاصة
دعم الباركود للمنتجات
نظام تصنيف للمنتجات (فئات رئيسية وفرعية)
أنظمة بيع متعددة (نقدي، آجل، أقساط)
ربط العملاء بالعقود والأقساط
إدارة صلاحيات المستخدمين
الملف يمثل وثيقة متطلبات لتطبيق إدارة مبيعات ومخزون شامل موجه للشركات التي تتعامل بالبيع المباشر والأقساط، مع التركيز على الواجهة العربية وتكامل جميع جوانب العمل التجاري.