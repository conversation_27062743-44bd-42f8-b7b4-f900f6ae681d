from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QTableView, QHeaderView, QMessageBox,
    QTabWidget, QComboBox, QDateEdit, QDialog
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QStandardItemModel, QStandardItem
import sqlite3
from add_contract_dialog import AddContractDialog

class ContractsPage(QWidget):
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_dummy_data()  # سيتم استبدالها بالبيانات الفعلية لاحقاً
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        
        # تبويب العقود
        contracts_tab = QWidget()
        self.setup_contracts_tab(contracts_tab)
        self.tabs.addTab(contracts_tab, "العقود")
        
        # تبويب الأقساط
        installments_tab = QWidget()
        self.setup_installments_tab(installments_tab)
        self.tabs.addTab(installments_tab, "الأقساط")
        
        layout.addWidget(self.tabs)
        
    def setup_contracts_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("بحث:")
        self.contract_search_edit = QLineEdit()
        self.contract_search_edit.setPlaceholderText("رقم العقد / اسم العميل")
        self.contract_search_edit.textChanged.connect(self.filter_contracts)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "منتهي", "ملغي"])
        self.status_filter.currentTextChanged.connect(self.filter_contracts)
        
        search_layout.addWidget(self.contract_search_edit)
        search_layout.addWidget(search_label)
        search_layout.addStretch()
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(status_label)
        
        # 2. أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_contract_btn = QPushButton("إضافة عقد")
        self.edit_contract_btn = QPushButton("تعديل")
        self.delete_contract_btn = QPushButton("حذف")
        
        self.add_contract_btn.clicked.connect(self.add_contract)
        self.edit_contract_btn.clicked.connect(self.edit_contract)
        self.delete_contract_btn.clicked.connect(self.delete_contract)
        
        actions_layout.addWidget(self.add_contract_btn)
        actions_layout.addWidget(self.edit_contract_btn)
        actions_layout.addWidget(self.delete_contract_btn)
        actions_layout.addStretch()
        
        # 3. جدول العقود
        self.contracts_table = QTableView()
        self.contracts_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.contracts_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.contracts_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.contracts_model = QStandardItemModel()
        self.contracts_model.setHorizontalHeaderLabels([
            "رقم العقد", "العميل", "البنك", "الفاتورة", "المبلغ الإجمالي",
            "قيمة القسط", "عدد الأقساط", "تاريخ البداية", "تاريخ النهاية", "الحالة"
        ])
        self.contracts_table.setModel(self.contracts_model)
        
        # إضافة العناصر للتخطيط
        layout.addLayout(search_layout)
        layout.addLayout(actions_layout)
        layout.addWidget(self.contracts_table)
        
    def setup_installments_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("بحث:")
        self.installment_search_edit = QLineEdit()
        self.installment_search_edit.setPlaceholderText("رقم العقد / اسم العميل")
        self.installment_search_edit.textChanged.connect(self.filter_installments)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        self.installment_status_filter = QComboBox()
        self.installment_status_filter.addItems(["الكل", "مستحق", "مدفوع", "متأخر"])
        self.installment_status_filter.currentTextChanged.connect(self.filter_installments)
        
        # فلتر التاريخ
        date_label = QLabel("التاريخ:")
        self.date_filter = QDateEdit()
        self.date_filter.setCalendarPopup(True)
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.dateChanged.connect(self.filter_installments)
        
        search_layout.addWidget(self.installment_search_edit)
        search_layout.addWidget(search_label)
        search_layout.addStretch()
        search_layout.addWidget(self.date_filter)
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.installment_status_filter)
        search_layout.addWidget(status_label)
        
        # 2. جدول الأقساط
        self.installments_table = QTableView()
        self.installments_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.installments_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.installments_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.installments_model = QStandardItemModel()
        self.installments_model.setHorizontalHeaderLabels([
            "رقم العقد", "العميل", "رقم القسط", "تاريخ الاستحقاق",
            "المبلغ", "المدفوع", "المتبقي", "الحالة", "تاريخ الدفع"
        ])
        self.installments_table.setModel(self.installments_model)
        
        # إضافة العناصر للتخطيط
        layout.addLayout(search_layout)
        layout.addWidget(self.installments_table)
        
    def load_dummy_data(self):
        """
        تحميل بيانات وهمية للعقود والأقساط
        """
        # بيانات العقود
        self.contracts = [
            {
                "id": "1",
                "contract_number": "CONT-001",
                "customer_name": "عميل 1",
                "bank_name": "البنك 1 - الفرع 1",
                "invoice_number": "INV-001",
                "total_amount": 12000.0,
                "installment_amount": 1000.0,
                "number_of_installments": 12,
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "status": "نشط"
            },
            {
                "id": "2",
                "contract_number": "CONT-002",
                "customer_name": "عميل 2",
                "bank_name": "البنك 2 - الفرع 1",
                "invoice_number": "INV-002",
                "total_amount": 24000.0,
                "installment_amount": 2000.0,
                "number_of_installments": 12,
                "start_date": "2024-02-01",
                "end_date": "2025-01-31",
                "status": "نشط"
            }
        ]
        
        # بيانات الأقساط
        self.installments = [
            {
                "contract_number": "CONT-001",
                "customer_name": "عميل 1",
                "installment_number": 1,
                "due_date": "2024-01-31",
                "amount": 1000.0,
                "paid_amount": 1000.0,
                "remaining_amount": 0.0,
                "status": "مدفوع",
                "payment_date": "2024-01-30"
            },
            {
                "contract_number": "CONT-001",
                "customer_name": "عميل 1",
                "installment_number": 2,
                "due_date": "2024-02-29",
                "amount": 1000.0,
                "paid_amount": 0.0,
                "remaining_amount": 1000.0,
                "status": "مستحق",
                "payment_date": None
            },
            {
                "contract_number": "CONT-002",
                "customer_name": "عميل 2",
                "installment_number": 1,
                "due_date": "2024-02-29",
                "amount": 2000.0,
                "paid_amount": 0.0,
                "remaining_amount": 2000.0,
                "status": "مستحق",
                "payment_date": None
            }
        ]
        
        self.display_contracts()
        self.display_installments()
        
    def display_contracts(self, search_text=None, status_filter=None):
        """
        عرض العقود في الجدول
        """
        self.contracts_model.clear()
        self.contracts_model.setHorizontalHeaderLabels([
            "رقم العقد", "العميل", "البنك", "الفاتورة", "المبلغ الإجمالي",
            "قيمة القسط", "عدد الأقساط", "تاريخ البداية", "تاريخ النهاية", "الحالة"
        ])
        
        try:
            # بناء الاستعلام
            query = """
                SELECT 
                    c.*,
                    cu.name as customer_name,
                    b.name || ' - ' || bb.name as bank_name,
                    i.invoice_number
                FROM contracts c
                JOIN customers cu ON cu.id = c.customer_id
                JOIN bank_branches bb ON bb.id = c.bank_branch_id
                JOIN banks b ON b.id = bb.main_bank_id
                JOIN invoices i ON i.id = c.invoice_id
                WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND (c.contract_number LIKE %s OR cu.name LIKE %s)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
                
            if status_filter and status_filter != "الكل":
                query += " AND c.status = %s"
                params.append(status_filter)
                
            query += " ORDER BY c.created_at DESC"
            
            # تنفيذ الاستعلام
            contracts = self.db.execute(query, params).fetchall()
            self.contracts = [dict(contract) for contract in contracts]
            
            # عرض النتائج
            for contract in self.contracts:
                row = []
                row.append(QStandardItem(contract["contract_number"]))
                row.append(QStandardItem(contract["customer_name"]))
                row.append(QStandardItem(contract["bank_name"]))
                row.append(QStandardItem(contract["invoice_number"]))
                row.append(QStandardItem(str(contract["total_amount"])))
                row.append(QStandardItem(str(contract["installment_amount"])))
                row.append(QStandardItem(str(contract["number_of_installments"])))
                row.append(QStandardItem(contract["start_date"]))
                row.append(QStandardItem(contract["end_date"]))
                row.append(QStandardItem(contract["status"]))
                
                self.contracts_model.appendRow(row)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل العقود: {str(e)}")
        
    def display_installments(self, search_text=None, status_filter=None, date_filter=None):
        """
        عرض الأقساط في الجدول
        """
        self.installments_model.clear()
        self.installments_model.setHorizontalHeaderLabels([
            "رقم العقد", "العميل", "رقم القسط", "تاريخ الاستحقاق",
            "المبلغ", "المدفوع", "المتبقي", "الحالة", "تاريخ الدفع"
        ])
        
        try:
            # بناء الاستعلام
            query = """
                SELECT 
                    i.*,
                    c.contract_number,
                    cu.name as customer_name
                FROM installments i
                JOIN contracts c ON c.id = i.contract_id
                JOIN customers cu ON cu.id = c.customer_id
                WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND (c.contract_number LIKE %s OR cu.name LIKE %s)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
                
            if status_filter and status_filter != "الكل":
                query += " AND i.status = %s"
                params.append(status_filter)
                
            if date_filter:
                query += " AND i.due_date = %s"
                params.append(date_filter)
                
            query += " ORDER BY c.contract_number, i.installment_number"
            
            # تنفيذ الاستعلام
            installments = self.db.execute(query, params).fetchall()
            self.installments = [dict(installment) for installment in installments]
            
            # عرض النتائج
            for installment in self.installments:
                row = []
                row.append(QStandardItem(installment["contract_number"]))
                row.append(QStandardItem(installment["customer_name"]))
                row.append(QStandardItem(str(installment["installment_number"])))
                row.append(QStandardItem(installment["due_date"]))
                row.append(QStandardItem(str(installment["amount"])))
                row.append(QStandardItem(str(installment["paid_amount"])))
                row.append(QStandardItem(str(installment["amount"] - installment["paid_amount"])))
                row.append(QStandardItem(installment["status"]))
                row.append(QStandardItem(installment["payment_date"] if installment["payment_date"] else ""))
                
                self.installments_model.appendRow(row)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأقساط: {str(e)}")
        
    def filter_contracts(self):
        """
        تصفية العقود حسب البحث والحالة
        """
        search_text = self.contract_search_edit.text().lower()
        status_filter = self.status_filter.currentText()
        
        self.display_contracts(search_text, status_filter)
        
    def filter_installments(self):
        """
        تصفية الأقساط حسب البحث والحالة والتاريخ
        """
        search_text = self.installment_search_edit.text().lower()
        status_filter = self.installment_status_filter.currentText()
        date_filter = self.date_filter.date().toString('yyyy-MM-dd')
        
        self.display_installments(search_text, status_filter, date_filter)
        
    def add_contract(self):
        """
        إضافة عقد جديد
        """
        dialog = AddContractDialog(self.db, parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            contract_data = dialog.get_contract_data()
            
            try:
                # إضافة العقد لقاعدة البيانات
                cursor = self.db.execute("""
                    INSERT INTO العقود (
                        رقم_العقد, العميل_المعرف, فرع_البنك_المعرف, الفاتورة_المعرف,
                        إجمالي_المبلغ, مبلغ_القسط, عدد_الأقساط,
                        تاريخ_البداية, تاريخ_النهاية, الحالة, ملاحظات
                    ) VALUES (%s, %s, %s, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    contract_data["contract_number"],
                    contract_data["customer_id"],
                    contract_data["bank_branch_id"],
                    contract_data["invoice_id"],
                    contract_data["total_amount"],
                    contract_data["installment_amount"],
                    contract_data["number_of_installments"],
                    contract_data["start_date"],
                    contract_data["end_date"],
                    contract_data["status"],
                    contract_data["notes"]
                ))
                contract_id = cursor.lastrowid
                
                # إنشاء الأقساط
                start_date = QDate.fromString(contract_data["start_date"], 'yyyy-MM-dd')
                amount = contract_data["installment_amount"]
                
                for i in range(contract_data["number_of_installments"]):
                    due_date = start_date.addMonths(i)
                    
                    self.db.execute("""
                        INSERT INTO الأقساط (
                            العقد_المعرف, رقم_القسط, تاريخ_الاستحقاق,
                            المبلغ, الحالة
                        ) VALUES (%s, %s, %s, ?, ?)
                    """, (
                        contract_id,
                        i + 1,
                        due_date.toString('yyyy-MM-dd'),
                        amount,
                        "مستحق"
                    ))
                    
                self.db.commit()
                self.display_contracts()
                self.display_installments()
                QMessageBox.information(self, "نجاح", "تم إضافة العقد بنجاح", QMessageBox.StandardButton.Ok)
                
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة العقد: {str(e)}")
            
    def edit_contract(self):
        """
        تعديل العقد المحدد
        """
        selected_rows = self.contracts_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد عقد لتعديله")
            return
            
        row_index = selected_rows[0].row()
        contract_id = self.contracts[row_index]["id"]
        
        try:
            contract_data = self.db.execute("""
                SELECT * FROM العقود WHERE المعرف = %s
            """, (contract_id,)).fetchone()
            
            if not contract_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على العقد")
                return
            
            dialog = AddContractDialog(self.db, dict(contract_data), parent=self)
            result = dialog.exec()
            if result == QDialog.DialogCode.Accepted:
                contract_data = dialog.get_contract_data()
                
                # تحديث بيانات العقد
                self.db.execute("""
                    UPDATE العقود SET
                        رقم_العقد = %s,
                        العميل_المعرف = %s,
                        فرع_البنك_المعرف = %s,
                        الفاتورة_المعرف = %s,
                        إجمالي_المبلغ = %s,
                        مبلغ_القسط = %s,
                        عدد_الأقساط = %s,
                        تاريخ_البداية = %s,
                        تاريخ_النهاية = %s,
                        الحالة = %s,
                        ملاحظات = %s
                    WHERE المعرف = %s
                """, (
                    contract_data["contract_number"],
                    contract_data["customer_id"],
                    contract_data["bank_branch_id"],
                    contract_data["invoice_id"],
                    contract_data["total_amount"],
                    contract_data["installment_amount"],
                    contract_data["number_of_installments"],
                    contract_data["start_date"],
                    contract_data["end_date"],
                    contract_data["status"],
                    contract_data["notes"],
                    contract_id
                ))
                self.db.commit()
                
                # تحديث الأقساط
                self.update_installments(contract_id, contract_data)
                
                self.display_contracts()
                self.display_installments()
                QMessageBox.information(self, "نجاح", "تم تعديل العقد بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل العقد: {str(e)}")
            
    def delete_contract(self):
        """
        حذف العقد المحدد
        """
        selected_rows = self.contracts_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد عقد لحذفه")
            return
            
        row_index = selected_rows[0].row()
        contract = self.contracts[row_index]
        
        # التحقق من وجود أقساط مدفوعة
        paid_installments = [
            i for i in self.installments
            if i["contract_number"] == contract["contract_number"] and i["status"] == "مدفوع"
        ]
        
        if paid_installments:
            QMessageBox.warning(
                self,
                "تعذر الحذف",
                "لا يمكن حذف العقد لأنه يحتوي على أقساط مدفوعة"
            )
            return
            
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العقد {contract['contract_number']}؟\nسيتم حذف جميع الأقساط المرتبطة به.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف العقد
            self.contracts.pop(row_index)
            
            # حذف الأقساط المرتبطة
            self.installments = [
                i for i in self.installments
                if i["contract_number"] != contract["contract_number"]
            ]
            
            self.display_contracts()
            self.display_installments()
            QMessageBox.information(self, "نجاح", "تم حذف العقد بنجاح", QMessageBox.StandardButton.Ok)
            
    def create_installments(self, contract):
        """
        إنشاء الأقساط للعقد الجديد
        """
        start_date = QDate.fromString(contract["start_date"], 'yyyy-MM-dd')
        amount = contract["installment_amount"]
        
        for i in range(contract["number_of_installments"]):
            due_date = start_date.addMonths(i)
            
            installment = {
                "contract_number": contract["contract_number"],
                "customer_name": contract["customer_name"],
                "installment_number": i + 1,
                "due_date": due_date.toString('yyyy-MM-dd'),
                "amount": amount,
                "paid_amount": 0.0,
                "remaining_amount": amount,
                "status": "مستحق",
                "payment_date": None
            }
            
            self.installments.append(installment)
            
    def update_installments(self, contract_id, contract_data):
        """
        تحديث الأقساط بعد تعديل العقد
        """
        # حذف الأقساط غير المدفوعة
        self.installments = [
            i for i in self.installments
            if i["contract_number"] != contract_data["contract_number"] or i["status"] == "مدفوع"
        ]
        
        # إعادة إنشاء الأقساط المتبقية
        paid_count = len([
            i for i in self.installments
            if i["contract_number"] == contract_data["contract_number"]
        ])
        
        start_date = QDate.fromString(contract_data["start_date"], 'yyyy-MM-dd').addMonths(paid_count)
        amount = contract_data["installment_amount"]
        
        for i in range(paid_count, contract_data["number_of_installments"]):
            due_date = start_date.addMonths(i - paid_count)
            
            installment = {
                "contract_number": contract_data["contract_number"],
                "customer_name": contract_data["customer_name"],
                "installment_number": i + 1,
                "due_date": due_date.toString('yyyy-MM-dd'),
                "amount": amount,
                "paid_amount": 0.0,
                "remaining_amount": amount,
                "status": "مستحق",
                "payment_date": None
            }
            
            self.installments.append(installment)
            
    def get_customer_name(self, customer_id):
        """الحصول على اسم العميل من قاعدة البيانات"""
        try:
            result = self.db.execute("SELECT الاسم FROM العملاء WHERE المعرف = %s", (customer_id,)).fetchone()
            return result[0] if result else ""
        except Exception as e:
            print(f"Error getting customer name: {str(e)}")
            return ""
            
    def get_bank_name(self, branch_id):
        """الحصول على اسم البنك والفرع من قاعدة البيانات"""
        try:
            result = self.db.execute("""
                SELECT b.name || ' - ' || bb.name
                FROM bank_branches bb
                JOIN banks b ON b.id = bb.main_bank_id
                WHERE bb.id = %s
            """, (branch_id,)).fetchone()
            return result[0] if result else ""
        except Exception as e:
            print(f"Error getting bank name: {str(e)}")
            return ""
            
    def get_invoice_number(self, invoice_id):
        """الحصول على رقم الفاتورة من قاعدة البيانات"""
        try:
            result = self.db.execute("SELECT رقم_الفاتورة FROM الفواتير WHERE المعرف = %s", (invoice_id,)).fetchone()
            return result[0] if result else ""
        except Exception as e:
            print(f"Error getting invoice number: {str(e)}")
            return "" 