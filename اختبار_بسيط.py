#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتأكد من عمل قاعدة البيانات العربية
"""

from database import Database

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("اختبار الاتصال بقاعدة البيانات...")
    try:
        db = Database()
        print("تم الاتصال بنجاح")
        db.close()
        return True
    except Exception as e:
        print(f"فشل الاتصال: {str(e)}")
        return False

def test_tables():
    """اختبار وجود الجداول"""
    print("اختبار وجود الجداول...")
    
    tables = ['الفئات', 'المنتجات', 'العملاء', 'الموظفين', 'الفواتير']
    
    try:
        db = Database()
        success = 0
        
        for table in tables:
            try:
                result = db.execute(f"SHOW TABLES LIKE '{table}'").fetchone()
                if result:
                    print(f"جدول {table}: موجود")
                    success += 1
                else:
                    print(f"جدول {table}: غير موجود")
            except Exception as e:
                print(f"خطأ في جدول {table}: {str(e)}")
        
        db.close()
        print(f"النتيجة: {success}/{len(tables)} جدول موجود")
        return success == len(tables)
        
    except Exception as e:
        print(f"خطأ: {str(e)}")
        return False

def test_insert():
    """اختبار إدراج البيانات"""
    print("اختبار إدراج البيانات...")
    
    try:
        db = Database()
        
        # حذف الفئة إذا كانت موجودة
        db.execute("DELETE FROM الفئات WHERE الاسم = 'فئة تجريبية'")
        db.commit()

        # إدراج فئة
        db.execute("INSERT INTO الفئات (الاسم, الوصف) VALUES ('فئة تجريبية', 'للاختبار')")
        db.commit()
        
        # التحقق من الإدراج
        result = db.execute("SELECT المعرف FROM الفئات WHERE الاسم = 'فئة تجريبية'").fetchone()
        if result:
            category_id = result['المعرف']
            print("تم إدراج الفئة بنجاح")
            
            # حذف الفئة
            db.execute("DELETE FROM الفئات WHERE المعرف = %s", (category_id,))
            db.commit()
            print("تم حذف الفئة التجريبية")
            
            db.close()
            return True
        else:
            print("فشل في إدراج الفئة")
            db.close()
            return False
            
    except Exception as e:
        print(f"خطأ في الإدراج: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("بدء الاختبارات")
    print("=" * 30)
    
    tests = [
        ("اختبار الاتصال", test_connection),
        ("اختبار الجداول", test_tables),
        ("اختبار الإدراج", test_insert)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{name}:")
        if test_func():
            passed += 1
            print("نجح")
        else:
            print("فشل")
    
    print("\n" + "=" * 30)
    print(f"النتيجة النهائية: {passed}/{total}")
    
    if passed == total:
        print("جميع الاختبارات نجحت!")
        return True
    else:
        print("بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    main()
