from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableView,
    QHeaderView, QMessageBox, QLabel, QLineEdit, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QDateEdit, QComboBox, QGroupBox
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QStandardItemModel, QStandardItem

class AddSupplierDialog(QDialog):
    """
    نافذة منبثقة لإضافة أو تعديل مورد
    """
    def __init__(self, supplier_data=None, parent=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        
        if supplier_data:
            self.setWindowTitle("تعديل بيانات المورد")
        else:
            self.setWindowTitle("إضافة مورد جديد")
        
        self.setup_ui()
        
        if supplier_data:
            self.populate_form()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        self.id_edit = QLineEdit()
        if not self.supplier_data:  # إذا كان إضافة جديدة
            self.id_edit.setPlaceholderText("سيتم إنشاؤه تلقائيًا")
            self.id_edit.setEnabled(False)
        form_layout.addRow("رقم المورد:", self.id_edit)
        
        self.name_edit = QLineEdit()
        form_layout.addRow("اسم المورد:", self.name_edit)
        
        self.phone_edit = QLineEdit()
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        self.address_edit = QLineEdit()
        form_layout.addRow("العنوان:", self.address_edit)
        
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("تاريخ الإضافة:", self.date_edit)
        
        self.status_active = True  # افتراضيًا نشط
        
        layout.addLayout(form_layout)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        self.button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(self.button_box)
    
    def populate_form(self):
        if not self.supplier_data:
            return
        
        self.id_edit.setText(str(self.supplier_data["المعرف"]))
        self.name_edit.setText(self.supplier_data["الاسم"])
        self.phone_edit.setText(self.supplier_data["الهاتف"])
        self.address_edit.setText(self.supplier_data["العنوان"])
        
        # تحويل التاريخ من نص إلى كائن QDate
        date_parts = self.supplier_data["تاريخ الإضافة"].split("-")
        if len(date_parts) == 3:
            year, month, day = map(int, date_parts)
            self.date_edit.setDate(QDate(year, month, day))
        
        self.status_active = self.supplier_data["الحالة"]
    
    def get_supplier_data(self):
        return {
            "المعرف": self.id_edit.text() if self.id_edit.text() else None,
            "الاسم": self.name_edit.text(),
            "الهاتف": self.phone_edit.text(),
            "العنوان": self.address_edit.text(),
            "تاريخ الإضافة": self.date_edit.date().toString("yyyy-MM-dd"),
            "الحالة": self.status_active
        }

class SuppliersPage(QWidget):
    """
    واجهة قسم الموردين
    """
    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.suppliers = []  # تهيئة قائمة الموردين
        self.invoices = []  # تهيئة قائمة الفواتير
        self.accounts = []  # تهيئة قائمة الحسابات
        self.setup_ui()
        
        # تحميل البيانات الأولية
        self.display_suppliers()
        self.display_invoices()
        self.display_accounts()
    
    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        
        # إنشاء تبويبات
        self.tabs = QTabWidget()
        
        # تبويب الموردين
        suppliers_tab = QWidget()
        self.setup_suppliers_tab(suppliers_tab)
        self.tabs.addTab(suppliers_tab, "الموردين")
        
        # تبويب فواتير الموردين
        invoices_tab = QWidget()
        self.setup_invoices_tab(invoices_tab)
        self.tabs.addTab(invoices_tab, "فواتير الموردين")
        
        # تبويب الحسابات
        accounts_tab = QWidget()
        self.setup_accounts_tab(accounts_tab)
        self.tabs.addTab(accounts_tab, "الحسابات")
        
        main_layout.addWidget(self.tabs)
    
    def setup_suppliers_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.supplier_search_edit = QLineEdit()
        self.supplier_search_edit.setPlaceholderText("اسم المورد أو رقم الهاتف")
        self.supplier_search_edit.textChanged.connect(self.filter_suppliers)
        search_layout.addWidget(self.supplier_search_edit)
        search_layout.addWidget(search_label)
        layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_supplier_button = QPushButton("إضافة مورد")
        self.edit_supplier_button = QPushButton("تعديل مورد")
        self.delete_supplier_button = QPushButton("حذف مورد")
        self.toggle_status_button = QPushButton("تغيير الحالة")
        self.add_payment_button = QPushButton("إضافة دفعة")
        self.view_payments_button = QPushButton("عرض المدفوعات")
        
        self.add_supplier_button.clicked.connect(self.add_supplier)
        self.edit_supplier_button.clicked.connect(self.edit_supplier)
        self.delete_supplier_button.clicked.connect(self.delete_supplier)
        self.toggle_status_button.clicked.connect(self.toggle_supplier_status)
        self.add_payment_button.clicked.connect(self.add_payment)
        self.view_payments_button.clicked.connect(self.view_payments)
        
        actions_layout.addWidget(self.add_supplier_button)
        actions_layout.addWidget(self.edit_supplier_button)
        actions_layout.addWidget(self.delete_supplier_button)
        actions_layout.addWidget(self.toggle_status_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.view_payments_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 3. جدول الموردين
        self.suppliers_table = QTableView()
        self.suppliers_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.suppliers_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.suppliers_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.suppliers_model = QStandardItemModel()
        self.suppliers_model.setHorizontalHeaderLabels(["المعرف", "الاسم", "رقم الهاتف", "العنوان", "البريد الإلكتروني", "ملاحظات"])
        self.suppliers_table.setModel(self.suppliers_model)
        
        layout.addWidget(self.suppliers_table)
    
    def setup_invoices_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.invoice_search_edit = QLineEdit()
        self.invoice_search_edit.setPlaceholderText("رقم الفاتورة أو اسم المورد")
        self.invoice_search_edit.textChanged.connect(self.filter_invoices)
        search_layout.addWidget(self.invoice_search_edit)
        search_layout.addWidget(search_label)
        layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_invoice_button = QPushButton("إضافة فاتورة")
        self.view_invoice_button = QPushButton("عرض فاتورة")
        self.delete_invoice_button = QPushButton("حذف فاتورة")
        self.add_invoice_payment_button = QPushButton("إضافة دفعة")
        
        self.add_invoice_button.clicked.connect(self.add_invoice)
        self.view_invoice_button.clicked.connect(self.view_invoice)
        self.delete_invoice_button.clicked.connect(self.delete_invoice)
        self.add_invoice_payment_button.clicked.connect(self.add_invoice_payment)
        
        actions_layout.addWidget(self.add_invoice_button)
        actions_layout.addWidget(self.view_invoice_button)
        actions_layout.addWidget(self.delete_invoice_button)
        actions_layout.addWidget(self.add_invoice_payment_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 3. جدول الفواتير
        self.invoices_table = QTableView()
        self.invoices_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.invoices_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.invoices_model = QStandardItemModel()
        self.invoices_model.setHorizontalHeaderLabels(["رقم الفاتورة", "المورد", "التاريخ", "المبلغ الإجمالي", "المدفوع", "المتبقي"])
        self.invoices_table.setModel(self.invoices_model)
        
        layout.addWidget(self.invoices_table)
    
    def setup_accounts_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.account_search_edit = QLineEdit()
        self.account_search_edit.setPlaceholderText("اسم المورد")
        self.account_search_edit.textChanged.connect(self.filter_accounts)
        search_layout.addWidget(self.account_search_edit)
        search_layout.addWidget(search_label)
        layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.print_statement_button = QPushButton("طباعة كشف حساب")
        self.export_statement_button = QPushButton("تصدير كشف حساب")
        
        self.print_statement_button.clicked.connect(self.print_statement)
        self.export_statement_button.clicked.connect(self.export_statement)
        
        actions_layout.addWidget(self.print_statement_button)
        actions_layout.addWidget(self.export_statement_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 3. جدول الحسابات
        self.accounts_table = QTableView()
        self.accounts_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.accounts_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.accounts_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.accounts_model = QStandardItemModel()
        self.accounts_model.setHorizontalHeaderLabels(["المورد", "إجمالي المشتريات", "المدفوع", "المتبقي"])
        self.accounts_table.setModel(self.accounts_model)
        
        layout.addWidget(self.accounts_table)
    
    def display_suppliers(self, suppliers=None):
        """
        عرض الموردين في الجدول
        """
        self.suppliers_model.clear()
        self.suppliers_model.setHorizontalHeaderLabels(["المعرف", "الاسم", "رقم الهاتف", "العنوان", "البريد الإلكتروني", "ملاحظات"])
        
        if suppliers is None:
            suppliers = self.suppliers
        
        for supplier in suppliers:
            row = []
            row.append(QStandardItem(str(supplier["المعرف"])))
            row.append(QStandardItem(supplier["الاسم"]))
            row.append(QStandardItem(supplier["الهاتف"]))
            row.append(QStandardItem(supplier["العنوان"]))
            row.append(QStandardItem(supplier.get("البريد_الإلكتروني", "")))
            row.append(QStandardItem(supplier.get("ملاحظات", "")))
            self.suppliers_model.appendRow(row)
    
    def display_invoices(self, invoices=None):
        """
        عرض فواتير الموردين في الجدول
        """
        self.invoices_model.clear()
        self.invoices_model.setHorizontalHeaderLabels(["رقم الفاتورة", "المورد", "التاريخ", "المبلغ الإجمالي", "المدفوع", "المتبقي"])
        
        if invoices is None and self.db is not None:
            try:
                # جلب الفواتير من قاعدة البيانات
                cursor = self.db.execute("""
                    SELECT si.*, s.الاسم as supplier_name,
                           (SELECT COALESCE(SUM(المبلغ), 0) FROM دفعات_الموردين WHERE الفاتورة_المعرف = si.المعرف) as paid_amount
                    FROM فواتير_الموردين si
                    JOIN الموردين s ON si.المورد_المعرف = s.المعرف
                    ORDER BY si.التاريخ DESC
                """)
                invoices = cursor.fetchall()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء جلب بيانات الفواتير: {str(e)}")
                return
        
        if invoices:
            for invoice in invoices:
                row = []
                row.append(QStandardItem(str(invoice["id"])))
                row.append(QStandardItem(invoice["supplier_name"]))
                row.append(QStandardItem(invoice["date"]))
                row.append(QStandardItem(f"{invoice['total_amount']:.2f}"))
                paid = invoice["paid_amount"]
                row.append(QStandardItem(f"{paid:.2f}"))
                remaining = invoice["total_amount"] - paid
                row.append(QStandardItem(f"{remaining:.2f}"))
                
                self.invoices_model.appendRow(row)
    
    def display_accounts(self, accounts=None):
        """
        عرض حسابات الموردين في الجدول
        """
        self.accounts_model.clear()
        self.accounts_model.setHorizontalHeaderLabels(["المورد", "إجمالي المشتريات", "المدفوع", "المتبقي"])
        
        if accounts is None and self.db is not None:
            try:
                # جلب حسابات الموردين من قاعدة البيانات
                cursor = self.db.execute("""
                    SELECT s.الاسم as supplier_name,
                           COALESCE(SUM(si.إجمالي_المبلغ), 0) as total_purchases,
                           COALESCE((SELECT SUM(المبلغ) FROM دفعات_الموردين sp WHERE sp.المورد_المعرف = s.المعرف), 0) as total_paid
                    FROM الموردين s
                    LEFT JOIN فواتير_الموردين si ON s.المعرف = si.المورد_المعرف
                    GROUP BY s.المعرف, s.الاسم
                    ORDER BY s.الاسم
                """)
                accounts = cursor.fetchall()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء جلب بيانات الحسابات: {str(e)}")
                return
        
        if accounts:
            for account in accounts:
                row = []
                row.append(QStandardItem(account["supplier_name"]))
                total_purchases = account["total_purchases"]
                row.append(QStandardItem(f"{total_purchases:.2f}"))
                total_paid = account["total_paid"]
                row.append(QStandardItem(f"{total_paid:.2f}"))
                remaining = total_purchases - total_paid
                row.append(QStandardItem(f"{remaining:.2f}"))
                
                self.accounts_model.appendRow(row)
    
    def filter_suppliers(self):
        """
        تصفية الموردين حسب البحث
        """
        search_text = self.supplier_search_edit.text().lower()
        
        if not search_text:
            self.display_suppliers()
            return
        
        filtered_suppliers = [
            supplier for supplier in self.suppliers
            if search_text in supplier["الاسم"].lower() or search_text in supplier["الهاتف"]
        ]
        
        self.display_suppliers(filtered_suppliers)
    
    def filter_invoices(self):
        """
        تصفية الفواتير حسب نص البحث
        """
        if self.db is None:
            return
            
        search_text = self.invoice_search_edit.text().strip()
        
        if not search_text:
            self.display_invoices()
            return
        
        try:
            cursor = self.db.execute("""
                SELECT si.*, s.الاسم as supplier_name,
                       (SELECT COALESCE(SUM(المبلغ), 0) FROM دفعات_الموردين WHERE الفاتورة_المعرف = si.المعرف) as paid_amount
                FROM فواتير_الموردين si
                JOIN الموردين s ON si.المورد_المعرف = s.المعرف
                WHERE si.المعرف LIKE %s OR s.الاسم LIKE %s
                ORDER BY si.التاريخ DESC
            """, (f"%{search_text}%", f"%{search_text}%"))

            filtered_invoices = cursor.fetchall()
            
            self.display_invoices(filtered_invoices)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def filter_accounts(self):
        """
        تصفية الحسابات حسب نص البحث
        """
        if self.db is None:
            return
            
        search_text = self.account_search_edit.text().strip()
        
        if not search_text:
            self.display_accounts()
            return
        
        try:
            cursor = self.db.execute("""
                SELECT s.الاسم as supplier_name,
                       COALESCE(SUM(si.إجمالي_المبلغ), 0) as total_purchases,
                       COALESCE((SELECT SUM(المبلغ) FROM دفعات_الموردين sp WHERE sp.المورد_المعرف = s.المعرف), 0) as total_paid
                FROM الموردين s
                LEFT JOIN فواتير_الموردين si ON s.المعرف = si.المورد_المعرف
                WHERE s.الاسم LIKE %s
                GROUP BY s.المعرف, s.الاسم
                ORDER BY s.الاسم
            """, (f"%{search_text}%",))

            filtered_accounts = cursor.fetchall()
            
            self.display_accounts(filtered_accounts)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def add_supplier(self):
        """
        إضافة مورد جديد
        """
        dialog = AddSupplierDialog(parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            new_supplier = dialog.get_supplier_data()
            new_id = max(int(supplier["المعرف"]) for supplier in self.suppliers) + 1 if self.suppliers else 1
            new_supplier["المعرف"] = str(new_id)
            self.suppliers.append(new_supplier)
            self.display_suppliers()
            QMessageBox.information(self, "نجاح", "تم إضافة المورد بنجاح", QMessageBox.StandardButton.Ok)
    
    def edit_supplier(self):
        """
        تعديل بيانات المورد المحدد
        """
        selected_rows = self.suppliers_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد مورد لتعديله", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        supplier_id = int(self.suppliers_model.item(row_index, 0).text())
        
        # البحث عن المورد في القائمة
        supplier = next((s for s in self.suppliers if int(s["المعرف"]) == supplier_id), None)
        if not supplier:
            return
        
        dialog = AddSupplierDialog(supplier, parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_supplier_data()
            
            # تحديث بيانات المورد
            for key, value in updated_data.items():
                supplier[key] = value
            
            self.display_suppliers()
            QMessageBox.information(self, "نجاح", "تم تعديل بيانات المورد بنجاح", QMessageBox.StandardButton.Ok)
    
    def delete_supplier(self):
        """
        حذف المورد المحدد
        """
        selected_rows = self.suppliers_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد مورد لحذفه", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        supplier_id = int(self.suppliers_model.item(row_index, 0).text())
        supplier_name = self.suppliers_model.item(row_index, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            f"هل أنت متأكد من حذف المورد {supplier_name}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف المورد من القائمة
            self.suppliers = [s for s in self.suppliers if int(s["المعرف"]) != supplier_id]
            self.display_suppliers()
            QMessageBox.information(self, "نجاح", "تم حذف المورد بنجاح", QMessageBox.StandardButton.Ok)
    
    def toggle_supplier_status(self):
        """
        تغيير حالة المورد (نشط/غير نشط)
        """
        selected_rows = self.suppliers_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد مورد لتغيير حالته", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        supplier_id = int(self.suppliers_model.item(row_index, 0).text())
        
        # البحث عن المورد في القائمة
        supplier = next((s for s in self.suppliers if int(s["المعرف"]) == supplier_id), None)
        if not supplier:
            return
        
        # تغيير الحالة
        supplier["الحالة"] = not supplier["الحالة"]
        
        self.display_suppliers()
        
        status_text = "نشط" if supplier["الحالة"] else "غير نشط"
        QMessageBox.information(self, "نجاح", f"تم تغيير حالة المورد إلى {status_text}", QMessageBox.StandardButton.Ok)
    
    def add_invoice(self):
        """
        إضافة فاتورة مورد جديدة
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        # التحقق من وجود موردين نشطين
        cursor = self.db.execute("SELECT المعرف, الاسم FROM الموردين WHERE نشط = 1 ORDER BY الاسم")
        active_suppliers = cursor.fetchall()
        
        if not active_suppliers:
            QMessageBox.warning(self, "خطأ", "لا يوجد موردين نشطين", QMessageBox.StandardButton.Ok)
            return
            
        dialog = AddSupplierInvoiceDialog(self.db, active_suppliers, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            invoice_data = dialog.get_invoice_data()
            
            try:
                cursor = self.db.cursor()
                # إضافة الفاتورة
                cursor.execute("""
                    INSERT INTO فواتير_الموردين (المورد_المعرف, التاريخ, إجمالي_المبلغ, ملاحظات)
                    VALUES (%s, %s, %s, ?)
                """, (invoice_data["supplier_id"], invoice_data["date"],
                      invoice_data["total_amount"], invoice_data["notes"]))

                invoice_id = cursor.lastrowid

                # إضافة الأصناف
                for item in invoice_data["items"]:
                    cursor.execute("""
                        INSERT INTO صفوف_فواتير_الموردين (الفاتورة_المعرف, المنتج_المعرف, الكمية, السعر)
                        VALUES (%s, %s, %s, ?)
                    """, (invoice_id, item["product_id"], item["quantity"], item["price"]))

                    # تحديث المخزون
                    cursor.execute("""
                        UPDATE المنتجات
                        SET الكمية = الكمية + ?
                        WHERE المعرف = %s
                    """, (item["quantity"], item["product_id"]))
                
                # إضافة الدفعة الأولى إذا وجدت
                if invoice_data["initial_payment"] > 0:
                    cursor.execute("""
                        INSERT INTO دفعات_الموردين (المورد_المعرف, الفاتورة_المعرف, المبلغ, التاريخ, ملاحظات)
                        VALUES (%s, %s, %s, ?, ?)
                    """, (invoice_data["supplier_id"], invoice_id,
                          invoice_data["initial_payment"], invoice_data["date"],
                          "دفعة أولى"))
                
                self.db.commit()
                self.display_invoices()  # تحديث الجدول
                QMessageBox.information(self, "نجاح", "تم إضافة الفاتورة بنجاح", QMessageBox.StandardButton.Ok)
                
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الفاتورة: {str(e)}")

    def view_invoice(self):
        """
        عرض تفاصيل فاتورة مورد
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        selected = self.invoices_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار فاتورة", QMessageBox.StandardButton.Ok)
            return
            
        invoice_id = self.invoices_model.item(selected[0].row(), 0).text()
        
        try:
            cursor = self.db.cursor()
            # جلب بيانات الفاتورة
            cursor.execute("""
                SELECT si.*, s.الاسم as supplier_name
                FROM فواتير_الموردين si
                JOIN الموردين s ON si.المورد_المعرف = s.المعرف
                WHERE si.المعرف = %s
            """, (invoice_id,))
            invoice = cursor.fetchone()
            
            if not invoice:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة", QMessageBox.StandardButton.Ok)
                return
            
            # جلب أصناف الفاتورة
            cursor.execute("""
                SELECT sii.*, p.الاسم as product_name
                FROM صفوف_فواتير_الموردين sii
                JOIN المنتجات p ON sii.المنتج_المعرف = p.المعرف
                WHERE sii.الفاتورة_المعرف = %s
            """, (invoice_id,))
            items = cursor.fetchall()

            # جلب المدفوعات
            cursor.execute("""
                SELECT * FROM دفعات_الموردين
                WHERE الفاتورة_المعرف = %s
                ORDER BY التاريخ
            """, (invoice_id,))
            payments = cursor.fetchall()
            dialog = ViewSupplierInvoiceDialog(invoice, items, payments, parent=self)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض الفاتورة: {str(e)}")

    def delete_invoice(self):
        """
        حذف فاتورة مورد
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        selected = self.invoices_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار فاتورة", QMessageBox.StandardButton.Ok)
            return
            
        invoice_id = self.invoices_model.item(selected[0].row(), 0).text()
        
        reply = QMessageBox.question(self, "تأكيد",
                                   "هل أنت متأكد من حذف الفاتورة؟\nسيتم حذف جميع المدفوعات المرتبطة بها",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                cursor = self.db.cursor()
                
                # جلب أصناف الفاتورة لتحديث المخزون
                cursor.execute("""
                    SELECT المنتج_المعرف, الكمية
                    FROM صفوف_فواتير_الموردين
                    WHERE الفاتورة_المعرف = %s
                """, (invoice_id,))
                items = cursor.fetchall()

                # تحديث المخزون
                for item in items:
                    cursor.execute("""
                        UPDATE المنتجات
                        SET الكمية = الكمية - ?
                        WHERE المعرف = %s
                    """, (item["الكمية"], item["المنتج_المعرف"]))

                # حذف المدفوعات
                cursor.execute("DELETE FROM دفعات_الموردين WHERE الفاتورة_المعرف = %s", (invoice_id,))

                # حذف أصناف الفاتورة
                cursor.execute("DELETE FROM صفوف_فواتير_الموردين WHERE الفاتورة_المعرف = %s", (invoice_id,))

                # حذف الفاتورة
                cursor.execute("DELETE FROM فواتير_الموردين WHERE المعرف = %s", (invoice_id,))
                
                self.db.commit()
                self.display_invoices()  # تحديث الجدول
                QMessageBox.information(self, "نجاح", "تم حذف الفاتورة بنجاح", QMessageBox.StandardButton.Ok)
                
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}")

    def add_payment(self):
        """
        إضافة دفعة للمورد
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        # التحقق من اختيار مورد
        selected = self.suppliers_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد", QMessageBox.StandardButton.Ok)
            return
            
        supplier_id = int(self.suppliers_model.item(selected[0].row(), 0).text())
        
        # التحقق من وجود المورد
        cursor = self.db.execute("SELECT المعرف, الاسم FROM الموردين WHERE المعرف = %s", (supplier_id,))
        supplier = cursor.fetchone()
        if not supplier:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد", QMessageBox.StandardButton.Ok)
            return
        
        # فتح نافذة إضافة دفعة
        dialog = AddSupplierPaymentDialog(self.db, supplier_id, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            payment_data = dialog.get_payment_data()
            
            try:
                cursor = self.db.cursor()
                cursor.execute("""
                    INSERT INTO دفعات_الموردين (المورد_المعرف, الفاتورة_المعرف, التاريخ, المبلغ, ملاحظات)
                    VALUES (%s, %s, %s, ?, ?)
                """, (payment_data["supplier_id"], payment_data["invoice_id"],
                      payment_data["date"], payment_data["amount"], payment_data["notes"]))
                
                self.db.commit()
                self.display_invoices()  # تحديث الجدول
                self.display_accounts()  # تحديث الحسابات
                
                QMessageBox.information(self, "نجاح", "تم إضافة الدفعة بنجاح", QMessageBox.StandardButton.Ok)
                
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الدفعة: {str(e)}")
    
    def add_invoice_payment(self):
        """
        إضافة دفعة لفاتورة محددة
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        # التحقق من اختيار فاتورة
        selected = self.invoices_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار فاتورة", QMessageBox.StandardButton.Ok)
            return
            
        invoice_id = self.invoices_model.item(selected[0].row(), 0).text()
        
        # التحقق من وجود الفاتورة
        cursor = self.db.cursor()
        cursor.execute("""
            SELECT si.*, s.الاسم as supplier_name
            FROM فواتير_الموردين si
            JOIN الموردين s ON si.المورد_المعرف = s.المعرف
            WHERE si.المعرف = %s
        """, (invoice_id,))
        invoice = cursor.fetchone()
        if not invoice:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة", QMessageBox.StandardButton.Ok)
            return
        
        # فتح نافذة إضافة دفعة
        dialog = AddSupplierPaymentDialog(self.db, invoice["supplier_id"], invoice_id, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            payment_data = dialog.get_payment_data()
            
            try:
                cursor = self.db.cursor()
                cursor.execute("""
                    INSERT INTO دفعات_الموردين (المورد_المعرف, الفاتورة_المعرف, التاريخ, المبلغ, ملاحظات)
                    VALUES (%s, %s, %s, ?, ?)
                """, (payment_data["supplier_id"], payment_data["invoice_id"],
                      payment_data["date"], payment_data["amount"], payment_data["notes"]))
                
                self.db.commit()
                self.display_invoices()  # تحديث الجدول
                self.display_accounts()  # تحديث الحسابات
                
                QMessageBox.information(self, "نجاح", "تم إضافة الدفعة بنجاح", QMessageBox.StandardButton.Ok)
                
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الدفعة: {str(e)}")
    
    def view_payments(self):
        """
        عرض مدفوعات المورد
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        # التحقق من اختيار مورد
        selected = self.suppliers_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد", QMessageBox.StandardButton.Ok)
            return
            
        supplier_id = int(self.suppliers_model.item(selected[0].row(), 0).text())
        supplier_name = self.suppliers_model.item(selected[0].row(), 1).text()
        
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT sp.*, si.المعرف as invoice_number
                FROM دفعات_الموردين sp
                LEFT JOIN فواتير_الموردين si ON sp.الفاتورة_المعرف = si.المعرف
                WHERE sp.المورد_المعرف = %s
                ORDER BY sp.التاريخ DESC
            """, (supplier_id,))
            
            payments = cursor.fetchall()
            if not payments:
                QMessageBox.information(self, "تنبيه", "لا توجد مدفوعات لهذا المورد", QMessageBox.StandardButton.Ok)
                return
            
            # إنشاء نص تقرير المدفوعات
            report = f"مدفوعات المورد: {supplier_name}\n\n"
            total_paid = 0
            
            for payment in payments:
                invoice_text = f"فاتورة رقم {payment['invoice_number']}" if payment["invoice_number"] else "دفعة عامة"
                report += f"""
                التاريخ: {payment['date']}
                {invoice_text}
                المبلغ: {payment['amount']:.2f}
                ملاحظات: {payment['notes'] or '-'}
                ------------------
                """
                total_paid += payment["amount"]
            
            report += f"\nإجمالي المدفوعات: {total_paid:.2f}"
            
            # عرض التقرير
            msg = QMessageBox(self)
            msg.setWindowTitle("تقرير المدفوعات")
            msg.setText(report)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المدفوعات: {str(e)}")

    def print_statement(self):
        """
        طباعة كشف حساب المورد
        """
        if self.db is None:
            QMessageBox.warning(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات", QMessageBox.StandardButton.Ok)
            return
            
        # التحقق من اختيار مورد
        selected = self.accounts_table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد", QMessageBox.StandardButton.Ok)
            return
            
        supplier_name = self.accounts_model.item(selected[0].row(), 0).text()
        
        try:
            cursor = self.db.cursor()
            
            # جلب بيانات المورد
            cursor.execute("""
                SELECT s.*,
                       COALESCE(SUM(si.إجمالي_المبلغ), 0) as total_purchases,
                       COALESCE((SELECT SUM(المبلغ) FROM دفعات_الموردين sp WHERE sp.المورد_المعرف = s.المعرف), 0) as total_paid
                FROM الموردين s
                LEFT JOIN فواتير_الموردين si ON s.المعرف = si.المورد_المعرف
                WHERE s.الاسم = %s
                GROUP BY s.المعرف
            """, (supplier_name,))
            
            supplier = cursor.fetchone()
            
            if not supplier:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد", QMessageBox.StandardButton.Ok)
                return
            
            # جلب الفواتير
            cursor.execute("""
                SELECT si.*,
                       (SELECT COALESCE(SUM(المبلغ), 0) FROM دفعات_الموردين WHERE الفاتورة_المعرف = si.المعرف) as paid_amount
                FROM فواتير_الموردين si
                WHERE si.المورد_المعرف = %s
                ORDER BY si.التاريخ
            """, (supplier["المعرف"],))

            invoices = cursor.fetchall()

            # جلب المدفوعات
            cursor.execute("""
                SELECT sp.*, si.المعرف as invoice_number
                FROM دفعات_الموردين sp
                LEFT JOIN فواتير_الموردين si ON sp.الفاتورة_المعرف = si.المعرف
                WHERE sp.المورد_المعرف = %s
                ORDER BY sp.التاريخ
            """, (supplier["المعرف"],))
            
            payments = cursor.fetchall()
            # إنشاء نص كشف الحساب
            statement = f"""
            كشف حساب المورد: {supplier["name"]}
            رقم الهاتف: {supplier["phone"]}
            العنوان: {supplier["address"]}
            
            إجمالي المشتريات: {supplier["total_purchases"]:.2f}
            إجمالي المدفوعات: {supplier["total_paid"]:.2f}
            الرصيد المتبقي: {supplier["total_purchases"] - supplier["total_paid"]:.2f}
            
            الفواتير:
            ------------------
            """
            
            for invoice in invoices:
                statement += f"""
                رقم الفاتورة: {invoice["id"]}
                التاريخ: {invoice["date"]}
                المبلغ: {invoice["total_amount"]:.2f}
                المدفوع: {invoice["paid_amount"]:.2f}
                المتبقي: {invoice["total_amount"] - invoice["paid_amount"]:.2f}
                ------------------
                """
            
            statement += "\nالمدفوعات:\n------------------\n"
            
            for payment in payments:
                invoice_text = f"فاتورة رقم {payment['invoice_number']}" if payment["invoice_number"] else "دفعة عامة"
                statement += f"""
                التاريخ: {payment["date"]}
                {invoice_text}
                المبلغ: {payment["amount"]:.2f}
                ملاحظات: {payment["notes"] or "-"}
                ------------------
                """
            
            # عرض كشف الحساب
            msg = QMessageBox(self)
            msg.setWindowTitle("كشف حساب")
            msg.setText(statement)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Save)
            
            if msg.exec() == QMessageBox.StandardButton.Save:
                self.export_statement()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء كشف الحساب: {str(e)}")
    
    def export_statement(self):
        """
        تصدير كشف حساب المورد
        """
        QMessageBox.information(self, "تنبيه", "هذه الميزة قيد التطوير", QMessageBox.StandardButton.Ok)

class AddSupplierInvoiceDialog(QDialog):
    """
    نافذة إضافة فاتورة مورد جديدة
    """
    def __init__(self, db, suppliers, parent=None):
        super().__init__(parent)
        self.db = db
        self.suppliers = suppliers
        self.items = []
        
        self.setWindowTitle("إضافة فاتورة مورد")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # معلومات الفاتورة الأساسية
        form_layout = QFormLayout()
        
        # المورد
        self.supplier_combo = QComboBox()
        for supplier in self.suppliers:
            self.supplier_combo.addItem(supplier["name"], supplier["id"])
        form_layout.addRow("المورد:", self.supplier_combo)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # الملاحظات
        self.notes_edit = QLineEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # جدول الأصناف
        items_group = QGroupBox("الأصناف")
        items_layout = QVBoxLayout(items_group)
        
        self.items_table = QTableView()
        self.items_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.items_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        
        self.items_model = QStandardItemModel()
        self.items_model.setHorizontalHeaderLabels(["الصنف", "الكمية", "السعر", "الإجمالي"])
        self.items_table.setModel(self.items_model)
        
        items_layout.addWidget(self.items_table)
        
        # أزرار إدارة الأصناف
        items_buttons_layout = QHBoxLayout()
        
        add_item_button = QPushButton("إضافة صنف")
        add_item_button.clicked.connect(self.add_item)
        
        remove_item_button = QPushButton("حذف صنف")
        remove_item_button.clicked.connect(self.remove_item)
        
        items_buttons_layout.addWidget(add_item_button)
        items_buttons_layout.addWidget(remove_item_button)
        items_buttons_layout.addStretch()
        
        items_layout.addLayout(items_buttons_layout)
        
        # إجمالي الفاتورة
        totals_layout = QHBoxLayout()
        self.total_label = QLabel("الإجمالي: 0.00")
        totals_layout.addWidget(self.total_label)
        totals_layout.addStretch()
        
        items_layout.addLayout(totals_layout)
        
        layout.addWidget(items_group)
        
        # الدفعة الأولى
        payment_layout = QHBoxLayout()
        payment_layout.addWidget(QLabel("الدفعة الأولى:"))
        self.initial_payment_edit = QLineEdit()
        self.initial_payment_edit.setPlaceholderText("0.00")
        self.initial_payment_edit.textChanged.connect(self.validate_payment)
        payment_layout.addWidget(self.initial_payment_edit)
        
        layout.addLayout(payment_layout)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def add_item(self):
        """
        إضافة صنف جديد للفاتورة
        """
        if self.db is None:
            return
            
        # جلب المنتجات النشطة
        cursor = self.db.execute("SELECT المعرف, الاسم, سعر_الشراء FROM المنتجات WHERE نشط = 1 ORDER BY الاسم")
        products = cursor.fetchall()
        if not products:
            QMessageBox.warning(self, "خطأ", "لا توجد منتجات نشطة", QMessageBox.StandardButton.Ok)
            return
            
        dialog = AddInvoiceItemDialog(products, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            item_data = dialog.get_item_data()
            
            # إضافة الصنف للقائمة
            self.items.append(item_data)
            
            # إضافة الصنف للجدول
            row = []
            row.append(QStandardItem(item_data["product_name"]))
            row.append(QStandardItem(str(item_data["quantity"])))
            row.append(QStandardItem(f"{item_data['price']:.2f}"))
            total = item_data["quantity"] * item_data["price"]
            row.append(QStandardItem(f"{total:.2f}"))
            
            self.items_model.appendRow(row)
            
            # تحديث الإجمالي
            self.update_total()
    
    def remove_item(self):
        """
        حذف الصنف المحدد من الفاتورة
        """
        selected = self.items_table.selectionModel().selectedRows()
        if not selected:
            return
            
        row = selected[0].row()
        self.items_model.removeRow(row)
        self.items.pop(row)
        
        # تحديث الإجمالي
        self.update_total()
    
    def update_total(self):
        """
        تحديث إجمالي الفاتورة
        """
        total = sum(item["quantity"] * item["price"] for item in self.items)
        self.total_label.setText(f"الإجمالي: {total:.2f}")
        
        # تحديث الحد الأقصى للدفعة الأولى
        self.validate_payment()
    
    def validate_payment(self):
        """
        التحقق من صحة مبلغ الدفعة الأولى
        """
        try:
            payment = float(self.initial_payment_edit.text() or 0)
            total = sum(item["quantity"] * item["price"] for item in self.items)
            
            if payment > total:
                self.initial_payment_edit.setText(str(total))
                
        except ValueError:
            self.initial_payment_edit.setText("0")
    
    def get_invoice_data(self):
        """
        الحصول على بيانات الفاتورة
        """
        return {
            "supplier_id": self.supplier_combo.currentData(),
            "date": self.date_edit.date().toString("yyyy-MM-dd"),
            "notes": self.notes_edit.text(),
            "items": self.items,
            "total_amount": sum(item["quantity"] * item["price"] for item in self.items),
            "initial_payment": float(self.initial_payment_edit.text() or 0)
        }

class AddInvoiceItemDialog(QDialog):
    """
    نافذة إضافة صنف للفاتورة
    """
    def __init__(self, products, parent=None):
        super().__init__(parent)
        self.products = products
        
        self.setWindowTitle("إضافة صنف")
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        # المنتج
        self.product_combo = QComboBox()
        for product in self.products:
            self.product_combo.addItem(product["الاسم"], product["المعرف"])
        self.product_combo.currentIndexChanged.connect(self.update_price)
        form_layout.addRow("المنتج:", self.product_combo)
        
        # الكمية
        self.quantity_edit = QLineEdit()
        self.quantity_edit.setText("1")
        self.quantity_edit.textChanged.connect(self.validate_quantity)
        form_layout.addRow("الكمية:", self.quantity_edit)
        
        # السعر
        self.price_edit = QLineEdit()
        self.price_edit.textChanged.connect(self.validate_price)
        form_layout.addRow("السعر:", self.price_edit)
        
        layout.addLayout(form_layout)
        
        # الإجمالي
        self.total_label = QLabel()
        layout.addWidget(self.total_label)
        
        # أزرار
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Save).setText("إضافة")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        
        # تحديث السعر الافتراضي
        self.update_price()
    
    def update_price(self):
        """
        تحديث السعر عند تغيير المنتج
        """
        product_id = self.product_combo.currentData()
        product = next(p for p in self.products if p["المعرف"] == product_id)
        self.price_edit.setText(str(product["سعر_الشراء"]))
    
    def validate_quantity(self):
        """
        التحقق من صحة الكمية
        """
        try:
            quantity = float(self.quantity_edit.text() or 0)
            if quantity <= 0:
                self.quantity_edit.setText("1")
            self.update_total()
        except ValueError:
            self.quantity_edit.setText("1")
    
    def validate_price(self):
        """
        التحقق من صحة السعر
        """
        try:
            price = float(self.price_edit.text() or 0)
            if price < 0:
                self.price_edit.setText("0")
            self.update_total()
        except ValueError:
            self.price_edit.setText("0")
    
    def update_total(self):
        """
        تحديث الإجمالي
        """
        try:
            quantity = float(self.quantity_edit.text() or 0)
            price = float(self.price_edit.text() or 0)
            total = quantity * price
            self.total_label.setText(f"الإجمالي: {total:.2f}")
        except ValueError:
            self.total_label.setText("الإجمالي: 0.00")
    
    def get_item_data(self):
        """
        الحصول على بيانات الصنف
        """
        product_id = self.product_combo.currentData()
        return {
            "product_id": product_id,
            "product_name": self.product_combo.currentText(),
            "quantity": float(self.quantity_edit.text()),
            "price": float(self.price_edit.text())
        }

class ViewSupplierInvoiceDialog(QDialog):
    """
    نافذة عرض تفاصيل فاتورة المورد
    """
    def __init__(self, invoice, items, payments, parent=None):
        super().__init__(parent)
        self.invoice = invoice
        self.items = items
        self.payments = payments
        
        self.setWindowTitle(f"تفاصيل الفاتورة رقم {invoice['id']}")
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # معلومات الفاتورة الأساسية
        info_group = QGroupBox("معلومات الفاتورة")
        info_layout = QFormLayout(info_group)
        
        info_layout.addRow("رقم الفاتورة:", QLabel(str(self.invoice["id"])))
        info_layout.addRow("المورد:", QLabel(self.invoice["supplier_name"]))
        info_layout.addRow("التاريخ:", QLabel(self.invoice["date"]))
        info_layout.addRow("الملاحظات:", QLabel(self.invoice["notes"] or "-"))
        
        layout.addWidget(info_group)
        
        # الأصناف
        items_group = QGroupBox("الأصناف")
        items_layout = QVBoxLayout(items_group)
        
        items_table = QTableView()
        items_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        items_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        
        items_model = QStandardItemModel()
        items_model.setHorizontalHeaderLabels(["الصنف", "الكمية", "السعر", "الإجمالي"])
        
        total_amount = 0
        for item in self.items:
            row = []
            row.append(QStandardItem(item["product_name"]))
            row.append(QStandardItem(str(item["quantity"])))
            row.append(QStandardItem(f"{item['price']:.2f}"))
            total = item["quantity"] * item["price"]
            total_amount += total
            row.append(QStandardItem(f"{total:.2f}"))
            items_model.appendRow(row)
        
        items_table.setModel(items_model)
        items_layout.addWidget(items_table)
        
        # إجمالي الأصناف
        total_label = QLabel(f"إجمالي الأصناف: {total_amount:.2f}")
        items_layout.addWidget(total_label)
        
        layout.addWidget(items_group)
        
        # المدفوعات
        payments_group = QGroupBox("المدفوعات")
        payments_layout = QVBoxLayout(payments_group)
        
        payments_table = QTableView()
        payments_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        payments_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        
        payments_model = QStandardItemModel()
        payments_model.setHorizontalHeaderLabels(["التاريخ", "المبلغ", "ملاحظات"])
        
        total_paid = 0
        for payment in self.payments:
            row = []
            row.append(QStandardItem(payment["date"]))
            amount = payment["amount"]
            total_paid += amount
            row.append(QStandardItem(f"{amount:.2f}"))
            row.append(QStandardItem(payment["notes"] or "-"))
            payments_model.appendRow(row)
        
        payments_table.setModel(payments_model)
        payments_layout.addWidget(payments_table)
        
        # ملخص المدفوعات
        summary_layout = QFormLayout()
        summary_layout.addRow("إجمالي المدفوع:", QLabel(f"{total_paid:.2f}"))
        remaining = total_amount - total_paid
        summary_layout.addRow("المتبقي:", QLabel(f"{remaining:.2f}"))
        
        payments_layout.addLayout(summary_layout)
        
        layout.addWidget(payments_group)
        
        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

class AddSupplierPaymentDialog(QDialog):
    """
    نافذة إضافة دفعة للمورد
    """
    def __init__(self, db, supplier_id, invoice_id=None, parent=None):
        super().__init__(parent)
        self.db = db
        self.supplier_id = supplier_id
        self.invoice_id = invoice_id
        
        self.setWindowTitle("إضافة دفعة")
        self.setup_ui()
        
        if invoice_id:
            self.load_invoice_data()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # المبلغ
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("0.00")
        self.amount_edit.textChanged.connect(self.validate_amount)
        form_layout.addRow("المبلغ:", self.amount_edit)
        
        # الملاحظات
        self.notes_edit = QLineEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # معلومات الفاتورة إذا وجدت
        if self.invoice_id:
            self.invoice_info = QLabel()
            layout.addWidget(self.invoice_info)
        
        # أزرار
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def load_invoice_data(self):
        """
        تحميل بيانات الفاتورة
        """
        if not self.db or not self.invoice_id:
            return
            
        try:
            cursor = self.db.cursor()
            
            # جلب بيانات الفاتورة
            cursor.execute("""
                SELECT total_amount,
                       (SELECT COALESCE(SUM(amount), 0) FROM supplier_payments WHERE invoice_id = %s) as paid_amount
                FROM supplier_invoices
                WHERE id = %s
            """, (self.invoice_id, self.invoice_id))
            
            invoice = cursor.fetchone()
            if invoice:
                total = invoice["total_amount"]
                paid = invoice["paid_amount"]
                remaining = total - paid
                
                self.invoice_info.setText(f"""
                    إجمالي الفاتورة: {total:.2f}
                    المدفوع: {paid:.2f}
                    المتبقي: {remaining:.2f}
                """)
                
                # تعيين المبلغ المتبقي كقيمة افتراضية
                self.amount_edit.setText(str(remaining))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفاتورة: {str(e)}")
    
    def validate_amount(self):
        """
        التحقق من صحة المبلغ
        """
        try:
            amount = float(self.amount_edit.text() or 0)
            if amount <= 0:
                self.amount_edit.setText("")
                
            elif self.invoice_id:
                # التحقق من عدم تجاوز المبلغ المتبقي
                cursor = self.db.cursor()
                cursor.execute("""
                    SELECT إجمالي_المبلغ,
                           (SELECT COALESCE(SUM(المبلغ), 0) FROM دفعات_الموردين WHERE الفاتورة_المعرف = %s) as paid_amount
                    FROM فواتير_الموردين
                    WHERE المعرف = %s
                """, (self.invoice_id, self.invoice_id))
                
                invoice = cursor.fetchone()
                if invoice:
                    remaining = invoice["total_amount"] - invoice["paid_amount"]
                    if amount > remaining:
                        self.amount_edit.setText(str(remaining))
            
        except ValueError:
            self.amount_edit.setText("")
    
    def get_payment_data(self):
        """
        الحصول على بيانات الدفعة
        """
        return {
            "supplier_id": self.supplier_id,
            "invoice_id": self.invoice_id,
            "date": self.date_edit.date().toString("yyyy-MM-dd"),
            "amount": float(self.amount_edit.text() or 0),
            "notes": self.notes_edit.text()
        }