import pymysql
import pymysql.cursors
from datetime import datetime

class Database:
    """فئة إدارة الاتصال بقاعدة البيانات"""
    
    def __init__(self):
        """تهيئة الاتصال بقاعدة البيانات"""
        self.conn = pymysql.connect(
            host="localhost",
            user="root",
            password="kh123456",
            database="نظام_المبيعات",
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        self.cursor = self.conn.cursor()
        
    def execute(self, query, params=()):
        """تنفيذ استعلام SQL"""
        self.cursor.execute(query, params)
        return self.cursor
        
    def fetchall(self):
        """استرجاع جميع النتائج"""
        return self.cursor.fetchall()
        
    def fetchone(self):
        """استرجاع صف واحد"""
        return self.cursor.fetchone()
        
    def commit(self):
        """حفظ التغييرات في قاعدة البيانات"""
        self.conn.commit()
        
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        self.conn.close()
        
    def __enter__(self):
        """دعم استخدام with statement"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق الاتصال تلقائياً عند الخروج من with block"""
        self.close()
        
    @property
    def lastrowid(self):
        """الحصول على معرف آخر صف تم إدراجه"""
        return self.cursor.lastrowid 