#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد الكاميرا والإضاءة للقبة
يقوم بضبط الكاميرا والإضاءة للحصول على أفضل منظر للقبة المغطاة بالقرميد
"""

import bpy
import mathutils
from mathutils import Vector
import math

class DomeCameraSetup:
    """فئة إعداد الكاميرا والإضاءة للقبة"""
    
    def __init__(self, dome_name="القبة", camera_name="Cam-001"):
        """تهيئة الإعداد"""
        self.dome_name = dome_name
        self.camera_name = camera_name
        self.dome = None
        self.camera = None
        
    def setup_objects(self):
        """إعداد الكائنات المطلوبة"""
        self.dome = bpy.data.objects.get(self.dome_name)
        self.camera = bpy.data.objects.get(self.camera_name)
        
        if not self.dome:
            print(f"خطأ: لم يتم العثور على كائن القبة '{self.dome_name}'")
            return False
            
        if not self.camera:
            print(f"خطأ: لم يتم العثور على الكاميرا '{self.camera_name}'")
            return False
            
        return True
    
    def position_camera_aerial(self):
        """وضع الكاميرا في منظر جوي للقبة"""
        if not self.setup_objects():
            return
            
        # حساب موقع الكاميرا فوق القبة وبعيداً عنها
        dome_center = self.dome.location.copy()
        dome_radius = max(self.dome.dimensions.x, self.dome.dimensions.y) / 2
        
        # وضع الكاميرا في زاوية 45 درجة
        distance = dome_radius * 3
        height = dome_radius * 2
        
        self.camera.location = (
            dome_center.x - distance * 0.7,
            dome_center.y - distance * 0.7,
            dome_center.z + height
        )
        
        # توجيه الكاميرا نحو القبة
        direction = dome_center - self.camera.location
        self.camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
        
        print(f"تم وضع الكاميرا في المنظر الجوي: {self.camera.location}")
    
    def position_camera_side(self):
        """وضع الكاميرا في منظر جانبي للقبة"""
        if not self.setup_objects():
            return
            
        dome_center = self.dome.location.copy()
        dome_radius = max(self.dome.dimensions.x, self.dome.dimensions.y) / 2
        
        # وضع الكاميرا على الجانب
        distance = dome_radius * 2.5
        
        self.camera.location = (
            dome_center.x - distance,
            dome_center.y,
            dome_center.z + dome_radius * 0.5
        )
        
        # توجيه الكاميرا نحو القبة
        direction = dome_center - self.camera.location
        self.camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
        
        print(f"تم وضع الكاميرا في المنظر الجانبي: {self.camera.location}")
    
    def position_camera_perspective(self):
        """وضع الكاميرا في منظر منظوري للقبة"""
        if not self.setup_objects():
            return
            
        dome_center = self.dome.location.copy()
        dome_radius = max(self.dome.dimensions.x, self.dome.dimensions.y) / 2
        
        # وضع الكاميرا في زاوية منظورية
        distance = dome_radius * 2.2
        
        self.camera.location = (
            dome_center.x - distance * 0.8,
            dome_center.y - distance * 0.6,
            dome_center.z + dome_radius * 1.2
        )
        
        # توجيه الكاميرا نحو القبة
        direction = dome_center - self.camera.location
        self.camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
        
        print(f"تم وضع الكاميرا في المنظر المنظوري: {self.camera.location}")
    
    def add_sun_light(self):
        """إضافة إضاءة الشمس"""
        # حذف الإضاءة الموجودة إن وجدت
        for obj in bpy.context.scene.objects:
            if obj.type == 'LIGHT' and obj.name.startswith('Sun'):
                bpy.data.objects.remove(obj, do_unlink=True)
        
        # إنشاء إضاءة الشمس
        bpy.ops.object.light_add(type='SUN')
        sun = bpy.context.active_object
        sun.name = "Sun_Light"
        
        # ضبط موقع واتجاه الشمس
        sun.location = (5, 5, 10)
        sun.rotation_euler = (math.radians(45), 0, math.radians(45))
        
        # ضبط قوة الإضاءة
        sun.data.energy = 3.0
        sun.data.color = (1.0, 0.95, 0.8)  # لون دافئ
        
        print("تم إضافة إضاءة الشمس")
    
    def add_area_light(self):
        """إضافة إضاءة منطقة للإضاءة الناعمة"""
        # إنشاء إضاءة منطقة
        bpy.ops.object.light_add(type='AREA')
        area_light = bpy.context.active_object
        area_light.name = "Area_Light"
        
        if self.dome:
            dome_center = self.dome.location.copy()
            dome_radius = max(self.dome.dimensions.x, self.dome.dimensions.y) / 2
            
            # وضع الإضاءة فوق القبة
            area_light.location = (
                dome_center.x,
                dome_center.y,
                dome_center.z + dome_radius * 3
            )
        
        # ضبط خصائص الإضاءة
        area_light.data.energy = 100
        area_light.data.size = 5
        area_light.data.color = (1.0, 1.0, 1.0)
        
        print("تم إضافة إضاءة المنطقة")
    
    def setup_render_settings(self):
        """ضبط إعدادات الرندر"""
        scene = bpy.context.scene
        
        # ضبط محرك الرندر
        scene.render.engine = 'CYCLES'
        
        # ضبط دقة الصورة
        scene.render.resolution_x = 1920
        scene.render.resolution_y = 1080
        scene.render.resolution_percentage = 100
        
        # ضبط عدد العينات
        scene.cycles.samples = 128
        
        print("تم ضبط إعدادات الرندر")
    
    def setup_complete(self, view_type="perspective"):
        """إعداد كامل للكاميرا والإضاءة"""
        print("بدء إعداد الكاميرا والإضاءة...")
        
        # وضع الكاميرا حسب النوع المطلوب
        if view_type == "aerial":
            self.position_camera_aerial()
        elif view_type == "side":
            self.position_camera_side()
        else:
            self.position_camera_perspective()
        
        # إضافة الإضاءة
        self.add_sun_light()
        self.add_area_light()
        
        # ضبط إعدادات الرندر
        self.setup_render_settings()
        
        # تحديث المشهد
        bpy.context.view_layer.update()
        
        print("تم الانتهاء من إعداد الكاميرا والإضاءة!")

# دوال سريعة للاستخدام
def setup_dome_camera(dome_name="القبة", camera_name="Cam-001", view_type="perspective"):
    """دالة سريعة لإعداد الكاميرا"""
    setup = DomeCameraSetup(dome_name, camera_name)
    setup.setup_complete(view_type)

def quick_aerial_view():
    """منظر جوي سريع"""
    setup_dome_camera(view_type="aerial")

def quick_side_view():
    """منظر جانبي سريع"""
    setup_dome_camera(view_type="side")

def quick_perspective_view():
    """منظر منظوري سريع"""
    setup_dome_camera(view_type="perspective")

# تنفيذ الإعداد إذا تم تشغيل السكريبت مباشرة
if __name__ == "__main__":
    setup_dome_camera()
