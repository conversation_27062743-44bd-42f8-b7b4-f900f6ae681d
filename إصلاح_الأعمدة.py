#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح الأعمدة المفقودة في الجداول
"""

from database import Database

def fix_missing_columns():
    """إضافة الأعمدة المفقودة"""
    try:
        db = Database()
        
        print("بدء إصلاح الأعمدة المفقودة...")
        
        # التحقق من وجود عمود الفئة_المعرف في جدول المنتجات
        try:
            db.execute("SELECT الفئة_المعرف FROM المنتجات LIMIT 1")
            print("عمود الفئة_المعرف موجود في جدول المنتجات")
        except Exception as e:
            print(f"عمود الفئة_المعرف مفقود، سيتم إضافته: {str(e)}")
            try:
                db.execute("ALTER TABLE المنتجات ADD COLUMN الفئة_المعرف INT AFTER الاسم")
                db.commit()
                print("تم إضافة عمود الفئة_المعرف بنجاح")
            except Exception as e2:
                print(f"خطأ في إضافة العمود: {str(e2)}")
        
        # التحقق من وجود عمود الكمية في جدول المنتجات
        try:
            db.execute("SELECT الكمية FROM المنتجات LIMIT 1")
            print("عمود الكمية موجود في جدول المنتجات")
        except Exception as e:
            print(f"عمود الكمية مفقود، سيتم إضافته: {str(e)}")
            try:
                db.execute("ALTER TABLE المنتجات ADD COLUMN الكمية INT DEFAULT 0 AFTER الكمية_الدنيا")
                db.commit()
                print("تم إضافة عمود الكمية بنجاح")
            except Exception as e2:
                print(f"خطأ في إضافة العمود: {str(e2)}")
        
        # التحقق من وجود عمود نشط في جدول المنتجات
        try:
            db.execute("SELECT نشط FROM المنتجات LIMIT 1")
            print("عمود نشط موجود في جدول المنتجات")
        except Exception as e:
            print(f"عمود نشط مفقود، سيتم إضافته: {str(e)}")
            try:
                db.execute("ALTER TABLE المنتجات ADD COLUMN نشط BOOLEAN DEFAULT 1 AFTER الكمية")
                db.commit()
                print("تم إضافة عمود نشط بنجاح")
            except Exception as e2:
                print(f"خطأ في إضافة العمود: {str(e2)}")
        
        # التحقق من وجود عمود نشط في جدول الموردين
        try:
            db.execute("SELECT نشط FROM الموردين LIMIT 1")
            print("عمود نشط موجود في جدول الموردين")
        except Exception as e:
            print(f"عمود نشط مفقود في الموردين، سيتم إضافته: {str(e)}")
            try:
                db.execute("ALTER TABLE الموردين ADD COLUMN نشط BOOLEAN DEFAULT 1 AFTER ملاحظات")
                db.commit()
                print("تم إضافة عمود نشط في جدول الموردين بنجاح")
            except Exception as e2:
                print(f"خطأ في إضافة العمود: {str(e2)}")
        
        # إضافة المفتاح الخارجي إذا لم يكن موجوداً
        try:
            db.execute("""
                ALTER TABLE المنتجات 
                ADD CONSTRAINT FK_المنتجات_الفئة 
                FOREIGN KEY (الفئة_المعرف) REFERENCES الفئات (المعرف) 
                ON DELETE SET NULL
            """)
            db.commit()
            print("تم إضافة المفتاح الخارجي للفئات")
        except Exception as e:
            print(f"المفتاح الخارجي موجود مسبقاً أو حدث خطأ: {str(e)}")
        
        print("انتهى إصلاح الأعمدة")
        db.close()
        
    except Exception as e:
        print(f"خطأ عام: {str(e)}")

if __name__ == "__main__":
    fix_missing_columns()
