from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableView,
    QHeaderView, QMessageBox, QLabel, QLineEdit, QFormLayout,
    QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItemModel, QStandardItem

class AddCustomerDialog(QDialog):
    """
    نافذة منبثقة لإضافة أو تعديل عميل
    """
    def __init__(self, customer_data=None, parent=None):
        super().__init__(parent)
        self.customer_data = customer_data
        
        if customer_data:
            self.setWindowTitle("تعديل بيانات العميل")
        else:
            self.setWindowTitle("إضافة عميل جديد")
        
        self.setup_ui()
        
        if customer_data:
            self.populate_form()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        form_layout.addRow("الاسم:", self.name_edit)
        
        self.phone_edit = QLineEdit()
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        self.address_edit = QLineEdit()
        form_layout.addRow("العنوان:", self.address_edit)
        
        self.id_proof_edit = QLineEdit()
        form_layout.addRow("إثبات الشخصية:", self.id_proof_edit)
        
        self.bank_edit = QLineEdit()
        form_layout.addRow("البنك:", self.bank_edit)
        
        self.account_edit = QLineEdit()
        form_layout.addRow("رقم الحساب:", self.account_edit)
        
        layout.addLayout(form_layout)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        self.button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(self.button_box)
    
    def populate_form(self):
        if not self.customer_data:
            return
        
        self.name_edit.setText(self.customer_data["الاسم"])
        self.phone_edit.setText(self.customer_data["الهاتف"])
        self.address_edit.setText(self.customer_data["العنوان"])
        
        if "إثبات_الشخصية" in self.customer_data:
            self.id_proof_edit.setText(self.customer_data["إثبات_الشخصية"])
        
        if "البنك" in self.customer_data:
            self.bank_edit.setText(self.customer_data["البنك"])
        
        if "رقم_الحساب" in self.customer_data:
            self.account_edit.setText(self.customer_data["رقم_الحساب"])
    
    def get_customer_data(self):
        return {
            "الاسم": self.name_edit.text(),
            "الهاتف": self.phone_edit.text(),
            "العنوان": self.address_edit.text(),
            "إثبات_الشخصية": self.id_proof_edit.text(),
            "البنك": self.bank_edit.text(),
            "رقم_الحساب": self.account_edit.text()
        }

class CustomersPage(QWidget):
    """
    واجهة قسم العملاء
    """
    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.customers = []  # تهيئة قائمة العملاء
        self.setup_ui()
        self.load_customers()  # تحميل العملاء من قاعدة البيانات
    
    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("اسم العميل أو رقم الهاتف")
        self.search_edit.textChanged.connect(self.filter_customers)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_label)
        main_layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_button = QPushButton("إضافة عميل")
        self.edit_button = QPushButton("تعديل عميل")
        self.delete_button = QPushButton("حذف عميل")
        
        self.add_button.clicked.connect(self.add_customer)
        self.edit_button.clicked.connect(self.edit_customer)
        self.delete_button.clicked.connect(self.delete_customer)
        
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addStretch()
        
        main_layout.addLayout(actions_layout)
        
        # 3. جدول العملاء
        self.customers_table = QTableView()
        self.customers_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.customers_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.customers_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.customers_model = QStandardItemModel()
        self.customers_model.setHorizontalHeaderLabels(["المعرف", "الاسم", "رقم الهاتف", "العنوان", "إثبات الشخصية", "البنك", "رقم الحساب"])
        self.customers_table.setModel(self.customers_model)
        
        main_layout.addWidget(self.customers_table)
    
    def display_customers(self, customers=None):
        """
        عرض العملاء في الجدول
        """
        self.customers_model.clear()
        self.customers_model.setHorizontalHeaderLabels(["المعرف", "الاسم", "رقم الهاتف", "العنوان", "إثبات الشخصية", "البنك", "رقم الحساب"])
        
        if customers is None:
            customers = self.customers
        
        for customer in customers:
            row = []
            row.append(QStandardItem(str(customer["المعرف"])))
            row.append(QStandardItem(customer["الاسم"]))
            row.append(QStandardItem(customer["الهاتف"]))
            row.append(QStandardItem(customer["العنوان"]))
            row.append(QStandardItem(customer.get("إثبات_الشخصية", "")))
            row.append(QStandardItem(customer.get("البنك", "")))
            row.append(QStandardItem(customer.get("رقم_الحساب", "")))
            
            self.customers_model.appendRow(row)
    
    def filter_customers(self):
        """
        تصفية العملاء حسب البحث
        """
        search_text = self.search_edit.text().lower()
        
        if not search_text:
            self.display_customers()
            return
        
        filtered_customers = [
            customer for customer in self.customers
            if search_text in customer["الاسم"].lower() or search_text in customer["الهاتف"]
        ]
        
        self.display_customers(filtered_customers)
    
    def add_customer(self):
        """
        إضافة عميل جديد
        """
        dialog = AddCustomerDialog(parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            new_customer = dialog.get_customer_data()
            new_id = max(int(customer["المعرف"]) for customer in self.customers) + 1 if self.customers else 1
            new_customer["المعرف"] = str(new_id)
            
            self.customers.append(new_customer)
            self.display_customers()
            QMessageBox.information(self, "نجاح", "تم إضافة العميل بنجاح", QMessageBox.StandardButton.Ok)
    
    def edit_customer(self):
        """
        تعديل بيانات العميل المحدد
        """
        selected_rows = self.customers_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد عميل لتعديله", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        customer_id = int(self.customers_model.item(row_index, 0).text())
        
        # البحث عن العميل في القائمة
        customer = next((c for c in self.customers if int(c["المعرف"]) == customer_id), None)
        if not customer:
            return
        
        dialog = AddCustomerDialog(customer, parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_customer_data()
            
            # تحديث بيانات العميل
            for key, value in updated_data.items():
                customer[key] = value
            
            self.display_customers()
            QMessageBox.information(self, "نجاح", "تم تعديل بيانات العميل بنجاح", QMessageBox.StandardButton.Ok)
    
    def delete_customer(self):
        """
        حذف العميل المحدد
        """
        selected_rows = self.customers_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد عميل لحذفه", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        customer_id = int(self.customers_model.item(row_index, 0).text())
        customer_name = self.customers_model.item(row_index, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            f"هل أنت متأكد من حذف العميل {customer_name}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف العميل من القائمة
            self.customers = [c for c in self.customers if int(c["المعرف"]) != customer_id]
            self.display_customers()
            QMessageBox.information(self, "نجاح", "تم حذف العميل بنجاح", QMessageBox.StandardButton.Ok)
    
    def load_customers(self):
        """
        تحميل العملاء من قاعدة البيانات
        """
        if self.db:
            try:
                cursor = self.db.execute("SELECT * FROM العملاء ORDER BY الاسم")
                self.customers = [dict(row) for row in cursor.fetchall()]
                self.display_customers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل العملاء: {str(e)}", QMessageBox.StandardButton.Ok)