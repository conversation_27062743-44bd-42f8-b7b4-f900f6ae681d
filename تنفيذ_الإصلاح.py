#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تنفيذ إصلاح الجداول
"""

import pymysql

def execute_sql_file():
    """تنفيذ ملف SQL لإصلاح الجداول"""
    try:
        # قراءة ملف SQL
        with open('إصلاح_الجداول.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # الاتصال بقاعدة البيانات
        conn = pymysql.connect(
            host="localhost",
            user="root",
            password="kh123456",
            database="نظام_المبيعات",
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        # تقسيم الأوامر وتنفيذها
        commands = sql_content.split(';')
        
        with conn.cursor() as cursor:
            for command in commands:
                command = command.strip()
                if command and not command.startswith('--'):
                    try:
                        cursor.execute(command)
                        print(f"تم تنفيذ: {command[:50]}...")
                    except Exception as e:
                        print(f"خطأ في تنفيذ: {command[:50]}... - {str(e)}")
            
            conn.commit()
        
        conn.close()
        print("تم إصلاح الجداول بنجاح!")
        
    except Exception as e:
        print(f"خطأ: {str(e)}")

if __name__ == "__main__":
    execute_sql_file()
