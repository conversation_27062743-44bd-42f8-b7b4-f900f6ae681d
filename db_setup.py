from database import Database
import pymysql
import pymysql.cursors

# إنشاء قاعدة البيانات
CREATE_DATABASE = "CREATE DATABASE IF NOT EXISTS نظام_المبيعات CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"

# جداول المخزون والمنتجات
INVENTORY_TABLES = [
    """
    CREATE TABLE IF NOT EXISTS الفئات (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL UNIQUE,
        الوصف TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS المنتجات (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL,
        الفئة_المعرف INT,
        الباركود VARCHAR(100) UNIQUE,
        الوصف TEXT,
        الوحدة VARCHAR(50) NOT NULL,
        سعر_الشراء DECIMAL(10,2) NOT NULL,
        سعر_البيع DECIMAL(10,2) NOT NULL,
        الكمية_الدنيا INT DEFAULT 0,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (الفئة_المعرف) REFERENCES الفئات (المعرف)
            ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS المخزون (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        المنتج_المعرف INT NOT NULL,
        الكمية INT NOT NULL DEFAULT 0,
        آخر_تحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
]

# جداول الموردين
SUPPLIERS_TABLES = [
    """
    CREATE TABLE IF NOT EXISTS الموردين (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL,
        الهاتف VARCHAR(50) NOT NULL,
        العنوان TEXT,
        البريد_الإلكتروني VARCHAR(255),
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS فواتير_الموردين (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        المورد_المعرف INT NOT NULL,
        التاريخ DATE NOT NULL,
        إجمالي_المبلغ DECIMAL(10,2) NOT NULL,
        المبلغ_المدفوع DECIMAL(10,2) DEFAULT 0,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS صفوف_فواتير_الموردين (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الفاتورة_المعرف INT NOT NULL,
        المنتج_المعرف INT NOT NULL,
        الكمية INT NOT NULL,
        سعر_الوحدة DECIMAL(10,2) NOT NULL,
        إجمالي_السعر DECIMAL(10,2) NOT NULL,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف)
            ON DELETE CASCADE,
        FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS دفعات_الموردين (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        المورد_المعرف INT NOT NULL,
        الفاتورة_المعرف INT,
        التاريخ DATE NOT NULL,
        المبلغ DECIMAL(10,2) NOT NULL,
        طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي') NOT NULL,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف)
            ON DELETE RESTRICT,
        FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف)
            ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
]

# جداول العملاء والمبيعات
SALES_TABLES = [
    """
    CREATE TABLE IF NOT EXISTS العملاء (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL,
        الهاتف VARCHAR(50) NOT NULL,
        العنوان TEXT,
        البريد_الإلكتروني VARCHAR(255),
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,

    """
    CREATE TABLE IF NOT EXISTS الفواتير (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        العميل_المعرف INT,
        الموظف_المعرف INT NOT NULL,
        التاريخ DATE NOT NULL,
        إجمالي_المبلغ DECIMAL(10,2) NOT NULL,
        الخصم DECIMAL(10,2) DEFAULT 0,
        المبلغ_النهائي DECIMAL(10,2) NOT NULL,
        المبلغ_المدفوع DECIMAL(10,2) DEFAULT 0,
        طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي', 'تقسيط') NOT NULL,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف)
            ON DELETE RESTRICT,
        FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS صفوف_الفواتير (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الفاتورة_المعرف INT NOT NULL,
        المنتج_المعرف INT NOT NULL,
        الكمية INT NOT NULL,
        سعر_الوحدة DECIMAL(10,2) NOT NULL,
        إجمالي_السعر DECIMAL(10,2) NOT NULL,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف)
            ON DELETE CASCADE,
        FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS دفعات_العملاء (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        العميل_المعرف INT NOT NULL,
        الفاتورة_المعرف INT,
        التاريخ DATE NOT NULL,
        المبلغ DECIMAL(10,2) NOT NULL,
        طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي') NOT NULL,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف)
            ON DELETE RESTRICT,
        FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف)
            ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
]

# جداول إضافية (البنوك، المصروفات، الصلاحيات، الحضور، الرواتب، العقود، الأقساط)
EXTRA_TABLES = [
    # البنوك
    """
    CREATE TABLE IF NOT EXISTS البنوك (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS فروع_البنوك (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        البنك_الرئيسي_المعرف INT NOT NULL,
        الاسم VARCHAR(255) NOT NULL,
        العنوان VARCHAR(255),
        رقم_الحساب VARCHAR(50),
        رقم_IBAN VARCHAR(50),
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (البنك_الرئيسي_المعرف) REFERENCES البنوك (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # فئات المصروفات والمصروفات
    """
    CREATE TABLE IF NOT EXISTS فئات_المصروفات (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(255) NOT NULL UNIQUE,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS المصروفات (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        التاريخ DATE NOT NULL,
        الفئة_المعرف INT NOT NULL,
        المبلغ DECIMAL(10,2) NOT NULL,
        الوصف VARCHAR(255) NOT NULL,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (الفئة_المعرف) REFERENCES فئات_المصروفات (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # الصلاحيات
    """
    CREATE TABLE IF NOT EXISTS الصلاحيات (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الاسم VARCHAR(100) NOT NULL,
        الوصف VARCHAR(255) NOT NULL,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    """
    CREATE TABLE IF NOT EXISTS صلاحيات_الموظفين (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الموظف_المعرف INT NOT NULL,
        الصلاحية_المعرف INT NOT NULL,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (الموظف_المعرف, الصلاحية_المعرف),
        FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
            ON DELETE CASCADE,
        FOREIGN KEY (الصلاحية_المعرف) REFERENCES الصلاحيات (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # الحضور والانصراف
    """
    CREATE TABLE IF NOT EXISTS الحضور (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الموظف_المعرف INT NOT NULL,
        وقت_الدخول DATETIME NOT NULL,
        وقت_الخروج DATETIME,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # الرواتب
    """
    CREATE TABLE IF NOT EXISTS الرواتب (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        الموظف_المعرف INT NOT NULL,
        الشهر TINYINT NOT NULL,
        السنة SMALLINT NOT NULL,
        الراتب_الأساسي DECIMAL(10,2) NOT NULL,
        العمولة DECIMAL(10,2) DEFAULT 0,
        الخصومات DECIMAL(10,2) DEFAULT 0,
        الإضافات DECIMAL(10,2) DEFAULT 0,
        تاريخ_الدفع DATETIME,
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (الموظف_المعرف, الشهر, السنة),
        FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # العقود
    """
    CREATE TABLE IF NOT EXISTS العقود (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        رقم_العقد VARCHAR(100) NOT NULL UNIQUE,
        العميل_المعرف INT NOT NULL,
        فرع_البنك_المعرف INT NOT NULL,
        الفاتورة_المعرف INT NOT NULL,
        إجمالي_المبلغ DECIMAL(10,2) NOT NULL,
        مبلغ_القسط DECIMAL(10,2) NOT NULL,
        عدد_الأقساط INT NOT NULL,
        تاريخ_البداية DATE NOT NULL,
        تاريخ_النهاية DATE NOT NULL,
        الحالة ENUM('نشط', 'منتهي', 'ملغي') NOT NULL DEFAULT 'نشط',
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف)
            ON DELETE RESTRICT,
        FOREIGN KEY (فرع_البنك_المعرف) REFERENCES فروع_البنوك (المعرف)
            ON DELETE RESTRICT,
        FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف)
            ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,
    # الأقساط
    """
    CREATE TABLE IF NOT EXISTS الأقساط (
        المعرف INT AUTO_INCREMENT PRIMARY KEY,
        العقد_المعرف INT NOT NULL,
        رقم_القسط INT NOT NULL,
        تاريخ_الاستحقاق DATE NOT NULL,
        المبلغ DECIMAL(10,2) NOT NULL,
        المبلغ_المدفوع DECIMAL(10,2) DEFAULT 0,
        تاريخ_الدفع DATE,
        الحالة ENUM('مستحق', 'مدفوع', 'متأخر') NOT NULL DEFAULT 'مستحق',
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (العقد_المعرف, رقم_القسط),
        FOREIGN KEY (العقد_المعرف) REFERENCES العقود (المعرف)
            ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """,

]

# أوامر تعديل الجداول
ALTERS = [
    # تحديث جدول العملاء
    "ALTER TABLE العملاء ADD COLUMN إثبات_الشخصية VARCHAR(100) AFTER العنوان",
    "ALTER TABLE العملاء ADD COLUMN البنك VARCHAR(255) AFTER إثبات_الشخصية",
    "ALTER TABLE العملاء ADD COLUMN رقم_الحساب VARCHAR(100) AFTER البنك",
    # تحديث جدول الموظفين
    "ALTER TABLE الموظفين ADD COLUMN تاريخ_التوظيف DATE AFTER العنوان",
    "ALTER TABLE الموظفين ADD COLUMN الراتب DECIMAL(10,2) DEFAULT 0 AFTER تاريخ_التوظيف",
    "ALTER TABLE الموظفين ADD COLUMN الحالة ENUM('نشط', 'غير نشط') DEFAULT 'نشط' AFTER الراتب",
    # تحديث جدول الفواتير
    "ALTER TABLE الفواتير ADD COLUMN رقم_الفاتورة VARCHAR(50) AFTER المعرف",
    "ALTER TABLE الفواتير ADD UNIQUE INDEX (رقم_الفاتورة)"
]

# بيانات الصلاحيات الأساسية
PERMISSIONS = [
    ("إدارة المبيعات", "صلاحية إدارة المبيعات ونقاط البيع"),
    ("إدارة المخزون", "صلاحية إدارة المخزون والمنتجات"),
    ("إدارة العملاء", "صلاحية إدارة بيانات العملاء"),
    ("إدارة الموردين", "صلاحية إدارة بيانات الموردين"),
    ("إدارة الموظفين", "صلاحية إدارة بيانات الموظفين"),
    ("إدارة المصروفات", "صلاحية إدارة المصروفات"),
    ("إدارة البنوك", "صلاحية إدارة البنوك وفروعها"),
    ("إدارة العقود", "صلاحية إدارة العقود والأقساط"),
    ("إدارة التقارير", "صلاحية عرض وطباعة التقارير"),
    ("إعدادات النظام", "صلاحية التحكم بإعدادات النظام")
]

def setup_database():
    """
    إنشاء قاعدة البيانات وجداولها
    """
    try:
        # إنشاء اتصال أولي بدون تحديد قاعدة بيانات
        conn = pymysql.connect(
            host="localhost",
            user="root",
            password="kh123456",
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with conn.cursor() as cursor:
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            cursor.execute(CREATE_DATABASE)
            conn.commit()
            
        # إغلاق الاتصال الأولي
        conn.close()
        
        # إنشاء اتصال جديد بقاعدة البيانات
        db = Database()
        
        # إنشاء جدول الموظفين أولاً (مطلوب للمفاتيح الخارجية في الجداول الأخرى)
        employees_table = """
        CREATE TABLE IF NOT EXISTS الموظفين (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الاسم VARCHAR(255) NOT NULL,
            الهاتف VARCHAR(50) NOT NULL,
            العنوان TEXT,
            البريد_الإلكتروني VARCHAR(255),
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        db.execute(employees_table)
        db.commit()
            
        # إنشاء جداول المخزون والمنتجات
        for table in INVENTORY_TABLES:
            db.execute(table)
            db.commit()
            
        # إنشاء جداول الموردين
        for table in SUPPLIERS_TABLES:
            db.execute(table)
            db.commit()
            
        # إنشاء جداول العملاء والمبيعات
        for table in SALES_TABLES:
            db.execute(table)
            db.commit()
            
        # إنشاء الجداول الإضافية
        for table in EXTRA_TABLES:
            db.execute(table)
            db.commit()
            
        # تنفيذ أوامر التعديل
        for alter in ALTERS:
            try:
                db.execute(alter)
                db.commit()
            except Exception:
                pass  # تجاهل الخطأ إذا كان الحقل موجود مسبقاً
        # إدخال بيانات الصلاحيات الأساسية
        for name, description in PERMISSIONS:
            try:
                db.execute(
                    "INSERT INTO الصلاحيات (الاسم, الوصف) VALUES (%s, %s)",
                    (name, description)
                )
                db.commit()
            except Exception:

                pass  # تجاهل الخطأ إذا كانت الصلاحية موجودة مسبقاً
        print("تم إنشاء قاعدة البيانات والجداول الإضافية بنجاح")
        
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء قاعدة البيانات: {str(e)}")
    finally:
        if 'db' in locals():
            db.close()

if __name__ == '__main__':
    setup_database()