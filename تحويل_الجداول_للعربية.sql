-- ملف تحويل أسماء الجداول من الإنجليزية إلى العربية
-- يجب تنفيذ هذا الملف على قاعدة البيانات الحالية لتحويل الجداول الإنجليزية

USE sales_system;

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- تحويل الجداول الأساسية أولاً
-- تحويل جدول categories إلى الفئات
ALTER TABLE categories RENAME TO الفئات;

-- تحويل جدول products إلى المنتجات
ALTER TABLE products RENAME TO المنتجات_جديد;

-- تحويل جدول inventory إلى المخزون
ALTER TABLE inventory RENAME TO المخزون_جديد;

-- تحويل جدول suppliers إلى الموردين
ALTER TABLE suppliers RENAME TO الموردين_جديد;

-- تحويل جدول customers إلى العملاء
ALTER TABLE customers RENAME TO العملاء_جديد;

-- تحويل جدول employees إلى الموظفين
ALTER TABLE employees RENAME TO الموظفين_جديد;

-- تحويل جدول invoices إلى الفواتير
ALTER TABLE invoices RENAME TO الفواتير_جديد;

-- تحويل جدول invoice_items إلى صفوف_الفواتير
ALTER TABLE invoice_items RENAME TO صفوف_الفواتير_جديد;

-- تحويل جدول customer_payments إلى دفعات_العملاء
ALTER TABLE customer_payments RENAME TO دفعات_العملاء_جديد;

-- تحويل جدول supplier_invoices إلى فواتير_الموردين
ALTER TABLE supplier_invoices RENAME TO فواتير_الموردين;

-- تحويل جدول supplier_invoice_items إلى صفوف_فواتير_الموردين
ALTER TABLE supplier_invoice_items RENAME TO صفوف_فواتير_الموردين_جديد;

-- تحويل جدول supplier_payments إلى دفعات_الموردين
ALTER TABLE supplier_payments RENAME TO دفعات_الموردين_جديد;

-- تحويل الجداول الإضافية
-- تحويل جدول banks إلى البنوك
ALTER TABLE banks RENAME TO البنوك_جديد;

-- تحويل جدول bank_branches إلى فروع_البنوك
ALTER TABLE bank_branches RENAME TO فروع_البنوك_جديد;

-- تحويل جدول expense_categories إلى فئات_المصروفات
ALTER TABLE expense_categories RENAME TO فئات_المصروفات_جديد;

-- تحويل جدول expenses إلى المصروفات
ALTER TABLE expenses RENAME TO المصروفات_جديد;

-- تحويل جدول permissions إلى الصلاحيات
ALTER TABLE permissions RENAME TO الصلاحيات_جديد;

-- تحويل جدول employee_permissions إلى صلاحيات_الموظفين
ALTER TABLE employee_permissions RENAME TO صلاحيات_الموظفين_جديد;

-- تحويل جدول attendance إلى الحضور
ALTER TABLE attendance RENAME TO الحضور_جديد;

-- تحويل جدول salaries إلى الرواتب
ALTER TABLE salaries RENAME TO الرواتب_جديد;

-- تحويل جدول contracts إلى العقود
ALTER TABLE contracts RENAME TO العقود_جديد;

-- تحويل جدول installments إلى الأقساط
ALTER TABLE installments RENAME TO الأقساط_جديد;

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- تحديث أسماء الأعمدة في الجداول المحولة
-- تحديث جدول الفئات
ALTER TABLE الفئات 
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN description الوصف TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول المنتجات_جديد
ALTER TABLE المنتجات_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN category_id الفئة_المعرف INT,
CHANGE COLUMN barcode الباركود VARCHAR(100),
CHANGE COLUMN description الوصف TEXT,
CHANGE COLUMN unit الوحدة VARCHAR(50),
CHANGE COLUMN purchase_price سعر_الشراء DECIMAL(10,2),
CHANGE COLUMN selling_price سعر_البيع DECIMAL(10,2),
CHANGE COLUMN min_quantity الكمية_الدنيا INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN active نشط BOOLEAN,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول المخزون_جديد
ALTER TABLE المخزون_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN last_updated آخر_تحديث TIMESTAMP;

-- تحديث جدول الموردين_جديد
ALTER TABLE الموردين_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN active نشط BOOLEAN,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول العملاء_جديد
ALTER TABLE العملاء_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN id_proof إثبات_الشخصية VARCHAR(100),
CHANGE COLUMN bank البنك VARCHAR(255),
CHANGE COLUMN account رقم_الحساب VARCHAR(100),
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول الموظفين_جديد
ALTER TABLE الموظفين_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN phone الهاتف VARCHAR(50),
CHANGE COLUMN address العنوان TEXT,
CHANGE COLUMN hire_date تاريخ_التوظيف DATE,
CHANGE COLUMN salary الراتب DECIMAL(10,2),
CHANGE COLUMN status الحالة ENUM('نشط', 'غير نشط'),
CHANGE COLUMN email البريد_الإلكتروني VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول الفواتير_جديد
ALTER TABLE الفواتير_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN invoice_number رقم_الفاتورة VARCHAR(50),
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN discount الخصم DECIMAL(10,2),
CHANGE COLUMN final_amount المبلغ_النهائي DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN payment_method طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي', 'تقسيط'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول صفوف_الفواتير_جديد
ALTER TABLE صفوف_الفواتير_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN unit_price سعر_الوحدة DECIMAL(10,2),
CHANGE COLUMN total_price إجمالي_السعر DECIMAL(10,2),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول دفعات_العملاء_جديد
ALTER TABLE دفعات_العملاء_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN payment_method طريقة_الدفع ENUM('نقداً', 'شيك', 'تحويل بنكي'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول فواتير_الموردين
ALTER TABLE فواتير_الموردين
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN supplier_id المورد_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول صفوف_فواتير_الموردين_جديد
ALTER TABLE صفوف_فواتير_الموردين_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN product_id المنتج_المعرف INT,
CHANGE COLUMN quantity الكمية INT,
CHANGE COLUMN price السعر DECIMAL(10,2),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول دفعات_الموردين_جديد
ALTER TABLE دفعات_الموردين_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN supplier_id المورد_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول البنوك_جديد
ALTER TABLE البنوك_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول فروع_البنوك_جديد
ALTER TABLE فروع_البنوك_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN main_bank_id البنك_الرئيسي_المعرف INT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN address العنوان VARCHAR(255),
CHANGE COLUMN account_number رقم_الحساب VARCHAR(50),
CHANGE COLUMN iban رقم_IBAN VARCHAR(50),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول فئات_المصروفات_جديد
ALTER TABLE فئات_المصروفات_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(255),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول المصروفات_جديد
ALTER TABLE المصروفات_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN date التاريخ DATE,
CHANGE COLUMN category_id الفئة_المعرف INT,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN description الوصف VARCHAR(255),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول الصلاحيات_جديد
ALTER TABLE الصلاحيات_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN name الاسم VARCHAR(100),
CHANGE COLUMN description الوصف VARCHAR(255),
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول صلاحيات_الموظفين_جديد
ALTER TABLE صلاحيات_الموظفين_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN permission_id الصلاحية_المعرف INT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول الحضور_جديد
ALTER TABLE الحضور_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN check_in وقت_الدخول DATETIME,
CHANGE COLUMN check_out وقت_الخروج DATETIME,
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول الرواتب_جديد
ALTER TABLE الرواتب_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN employee_id الموظف_المعرف INT,
CHANGE COLUMN month الشهر TINYINT,
CHANGE COLUMN year السنة SMALLINT,
CHANGE COLUMN base_salary الراتب_الأساسي DECIMAL(10,2),
CHANGE COLUMN commission العمولة DECIMAL(10,2),
CHANGE COLUMN deductions الخصومات DECIMAL(10,2),
CHANGE COLUMN additions الإضافات DECIMAL(10,2),
CHANGE COLUMN payment_date تاريخ_الدفع DATETIME,
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP;

-- تحديث جدول العقود_جديد
ALTER TABLE العقود_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN contract_number رقم_العقد VARCHAR(100),
CHANGE COLUMN customer_id العميل_المعرف INT,
CHANGE COLUMN bank_branch_id فرع_البنك_المعرف INT,
CHANGE COLUMN invoice_id الفاتورة_المعرف INT,
CHANGE COLUMN total_amount إجمالي_المبلغ DECIMAL(10,2),
CHANGE COLUMN installment_amount مبلغ_القسط DECIMAL(10,2),
CHANGE COLUMN number_of_installments عدد_الأقساط INT,
CHANGE COLUMN start_date تاريخ_البداية DATE,
CHANGE COLUMN end_date تاريخ_النهاية DATE,
CHANGE COLUMN status الحالة ENUM('نشط', 'منتهي', 'ملغي'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- تحديث جدول الأقساط_جديد
ALTER TABLE الأقساط_جديد
CHANGE COLUMN id المعرف INT AUTO_INCREMENT,
CHANGE COLUMN contract_id العقد_المعرف INT,
CHANGE COLUMN installment_number رقم_القسط INT,
CHANGE COLUMN due_date تاريخ_الاستحقاق DATE,
CHANGE COLUMN amount المبلغ DECIMAL(10,2),
CHANGE COLUMN paid_amount المبلغ_المدفوع DECIMAL(10,2),
CHANGE COLUMN payment_date تاريخ_الدفع DATE,
CHANGE COLUMN status الحالة ENUM('مستحق', 'مدفوع', 'متأخر'),
CHANGE COLUMN notes ملاحظات TEXT,
CHANGE COLUMN created_at تاريخ_الإنشاء TIMESTAMP,
CHANGE COLUMN updated_at تاريخ_التحديث TIMESTAMP;

-- إزالة الجداول القديمة إذا كانت موجودة وإعادة تسمية الجداول الجديدة
-- حذف الجداول العربية القديمة إذا كانت موجودة
DROP TABLE IF EXISTS المنتجات;
DROP TABLE IF EXISTS المخزون;
DROP TABLE IF EXISTS الموردين;
DROP TABLE IF EXISTS العملاء;
DROP TABLE IF EXISTS الموظفين;
DROP TABLE IF EXISTS الفواتير;
DROP TABLE IF EXISTS صفوف_الفواتير;
DROP TABLE IF EXISTS دفعات_العملاء;
DROP TABLE IF EXISTS صفوف_فواتير_الموردين;
DROP TABLE IF EXISTS دفعات_الموردين;
DROP TABLE IF EXISTS البنوك;
DROP TABLE IF EXISTS فروع_البنوك;
DROP TABLE IF EXISTS فئات_المصروفات;
DROP TABLE IF EXISTS المصروفات;
DROP TABLE IF EXISTS الصلاحيات;
DROP TABLE IF EXISTS صلاحيات_الموظفين;
DROP TABLE IF EXISTS الحضور;
DROP TABLE IF EXISTS الرواتب;
DROP TABLE IF EXISTS العقود;
DROP TABLE IF EXISTS الأقساط;

-- إعادة تسمية الجداول الجديدة للأسماء النهائية
ALTER TABLE المنتجات_جديد RENAME TO المنتجات;
ALTER TABLE المخزون_جديد RENAME TO المخزون;
ALTER TABLE الموردين_جديد RENAME TO الموردين;
ALTER TABLE العملاء_جديد RENAME TO العملاء;
ALTER TABLE الموظفين_جديد RENAME TO الموظفين;
ALTER TABLE الفواتير_جديد RENAME TO الفواتير;
ALTER TABLE صفوف_الفواتير_جديد RENAME TO صفوف_الفواتير;
ALTER TABLE دفعات_العملاء_جديد RENAME TO دفعات_العملاء;
ALTER TABLE صفوف_فواتير_الموردين_جديد RENAME TO صفوف_فواتير_الموردين;
ALTER TABLE دفعات_الموردين_جديد RENAME TO دفعات_الموردين;
ALTER TABLE البنوك_جديد RENAME TO البنوك;
ALTER TABLE فروع_البنوك_جديد RENAME TO فروع_البنوك;
ALTER TABLE فئات_المصروفات_جديد RENAME TO فئات_المصروفات;
ALTER TABLE المصروفات_جديد RENAME TO المصروفات;
ALTER TABLE الصلاحيات_جديد RENAME TO الصلاحيات;
ALTER TABLE صلاحيات_الموظفين_جديد RENAME TO صلاحيات_الموظفين;
ALTER TABLE الحضور_جديد RENAME TO الحضور;
ALTER TABLE الرواتب_جديد RENAME TO الرواتب;
ALTER TABLE العقود_جديد RENAME TO العقود;
ALTER TABLE الأقساط_جديد RENAME TO الأقساط;

-- إعادة إنشاء المفاتيح الخارجية بالأسماء العربية
-- مفاتيح جدول المنتجات
ALTER TABLE المنتجات
ADD CONSTRAINT FK_المنتجات_الفئة FOREIGN KEY (الفئة_المعرف) REFERENCES الفئات (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول المخزون
ALTER TABLE المخزون
ADD CONSTRAINT FK_المخزون_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الفواتير
ALTER TABLE الفواتير
ADD CONSTRAINT FK_الفواتير_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_الفواتير_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صفوف_الفواتير
ALTER TABLE صفوف_الفواتير
ADD CONSTRAINT FK_صفوف_الفواتير_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صفوف_الفواتير_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول دفعات_العملاء
ALTER TABLE دفعات_العملاء
ADD CONSTRAINT FK_دفعات_العملاء_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_دفعات_العملاء_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول فواتير_الموردين
ALTER TABLE فواتير_الموردين
ADD CONSTRAINT FK_فواتير_الموردين_المورد FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صفوف_فواتير_الموردين
ALTER TABLE صفوف_فواتير_الموردين
ADD CONSTRAINT FK_صفوف_فواتير_الموردين_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صفوف_فواتير_الموردين_المنتج FOREIGN KEY (المنتج_المعرف) REFERENCES المنتجات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول دفعات_الموردين
ALTER TABLE دفعات_الموردين
ADD CONSTRAINT FK_دفعات_الموردين_المورد FOREIGN KEY (المورد_المعرف) REFERENCES الموردين (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_دفعات_الموردين_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES فواتير_الموردين (المعرف) ON DELETE SET NULL;

-- مفاتيح جدول فروع_البنوك
ALTER TABLE فروع_البنوك
ADD CONSTRAINT FK_فروع_البنوك_البنك_الرئيسي FOREIGN KEY (البنك_الرئيسي_المعرف) REFERENCES البنوك (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول المصروفات
ALTER TABLE المصروفات
ADD CONSTRAINT FK_المصروفات_الفئة FOREIGN KEY (الفئة_المعرف) REFERENCES فئات_المصروفات (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول صلاحيات_الموظفين
ALTER TABLE صلاحيات_الموظفين
ADD CONSTRAINT FK_صلاحيات_الموظفين_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE,
ADD CONSTRAINT FK_صلاحيات_الموظفين_الصلاحية FOREIGN KEY (الصلاحية_المعرف) REFERENCES الصلاحيات (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الحضور
ALTER TABLE الحضور
ADD CONSTRAINT FK_الحضور_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول الرواتب
ALTER TABLE الرواتب
ADD CONSTRAINT FK_الرواتب_الموظف FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف) ON DELETE CASCADE;

-- مفاتيح جدول العقود
ALTER TABLE العقود
ADD CONSTRAINT FK_العقود_العميل FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_العقود_فرع_البنك FOREIGN KEY (فرع_البنك_المعرف) REFERENCES فروع_البنوك (المعرف) ON DELETE RESTRICT,
ADD CONSTRAINT FK_العقود_الفاتورة FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف) ON DELETE RESTRICT;

-- مفاتيح جدول الأقساط
ALTER TABLE الأقساط
ADD CONSTRAINT FK_الأقساط_العقد FOREIGN KEY (العقد_المعرف) REFERENCES العقود (المعرف) ON DELETE CASCADE;

-- تم الانتهاء من تحويل قاعدة البيانات إلى اللغة العربية
SELECT 'تم تحويل قاعدة البيانات إلى اللغة العربية بنجاح' AS النتيجة;
