تعليمات استخدام سكريبت توزيع القرميد على القبة
=============================================

الملفات المنشأة:
1. dome_tile_distributor.py - السكريبت الرئيسي لتوزيع القرميد
2. dome_camera_setup.py - سكريبت إعداد الكاميرا والإضاءة

المتطلبات:
- كائن قبة باسم "القبة"
- كائن قرميد باسم "قرميد"
- برنامج بلندر مع Python API

طريقة الاستخدام:

1. الاستخدام السريع:
   - افتح بلندر
   - تأكد من وجود كائن القبة والقرميد
   - شغل السكريبت dome_tile_distributor.py
   - سيتم توزيع القرميد تلقائياً

2. الاستخدام المتقدم:
   من خلال Python Console في بلندر:
   
   # استيراد السكريبت
   exec(open("dome_tile_distributor.py").read())
   
   # توزيع القرميد
   distribute_tiles_on_dome("القبة", "قرميد")
   
   # أو استخدام الفئة مباشرة
   distributor = DomeTileDistributor("القبة", "قرميد")
   distributor.distribute_complete()

3. إعداد الكاميرا والإضاءة:
   exec(open("dome_camera_setup.py").read())
   
   # منظر منظوري
   setup_dome_camera(view_type="perspective")
   
   # منظر جوي
   setup_dome_camera(view_type="aerial")
   
   # منظر جانبي
   setup_dome_camera(view_type="side")

الميزات:
- توزيع القرميد في صفوف دائرية
- نمط الطوب المتداخل
- تنويع واقعي في المواقع والأحجام
- قرميدة مركزية في الأعلى
- إعداد تلقائي للكاميرا والإضاءة

النتائج:
- يتم إنشاء مجموعة "Tiles_Collection" تحتوي على جميع القرميد
- القرميد الأصلي يتم إخفاؤه
- يتم إنشاء حوالي 400 قرميدة حسب حجم القبة

ملاحظات:
- تأكد من أن أسماء الكائنات صحيحة
- يمكن تعديل المعاملات في السكريبت حسب الحاجة
- السكريبت يحذف التوزيع السابق عند التشغيل مرة أخرى

للمساعدة أو التطوير:
- راجع التعليقات في الكود
- يمكن تعديل المعاملات في بداية كل دالة
- استخدم print() لمتابعة التقدم
