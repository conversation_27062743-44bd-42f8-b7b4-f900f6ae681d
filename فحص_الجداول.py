#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص بنية الجداول في قاعدة البيانات
"""

from database import Database

def check_table_structure():
    """فحص بنية الجداول"""
    try:
        db = Database()
        
        # فحص جدول المنتجات
        print("بنية جدول المنتجات:")
        print("=" * 40)
        cursor = db.execute("DESCRIBE المنتجات")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"العمود: {column['Field']}, النوع: {column['Type']}")
        
        print("\n" + "=" * 40)
        
        # فحص جدول الفئات
        print("بنية جدول الفئات:")
        print("=" * 40)
        cursor = db.execute("DESCRIBE الفئات")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"العمود: {column['Field']}, النوع: {column['Type']}")
        
        print("\n" + "=" * 40)
        
        # فحص جميع الجداول الموجودة
        print("جميع الجداول الموجودة:")
        print("=" * 40)
        cursor = db.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = list(table.values())[0]
            print(f"جدول: {table_name}")
        
        db.close()
        
    except Exception as e:
        print(f"خطأ: {str(e)}")

if __name__ == "__main__":
    check_table_structure()
