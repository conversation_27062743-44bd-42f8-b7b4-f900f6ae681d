from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableView,
    QHeaderView, QMessageBox, QLabel, QLineEdit, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QComboBox, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItemModel, QStandardItem

class AddProductDialog(QDialog):
    """
    نافذة منبثقة لإضافة أو تعديل منتج
    """
    def __init__(self, product_data=None, parent=None):
        super().__init__(parent)
        self.product_data = product_data
        
        if product_data:
            self.setWindowTitle("تعديل بيانات المنتج")
        else:
            self.setWindowTitle("إضافة منتج جديد")
        
        self.setup_ui()
        
        if product_data:
            self.populate_form()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        self.barcode_edit = QLineEdit()
        form_layout.addRow("الباركود:", self.barcode_edit)
        
        self.name_edit = QLineEdit()
        form_layout.addRow("اسم المنتج:", self.name_edit)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems(["فئة 1", "فئة 2", "فئة 3"])
        form_layout.addRow("الفئة:", self.category_combo)
        
        self.purchase_price_spin = QDoubleSpinBox()
        self.purchase_price_spin.setRange(0, 1000000)
        self.purchase_price_spin.setDecimals(2)
        form_layout.addRow("سعر الشراء:", self.purchase_price_spin)
        
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0, 1000000)
        self.selling_price_spin.setDecimals(2)
        form_layout.addRow("سعر البيع:", self.selling_price_spin)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(0, 10000)
        form_layout.addRow("الكمية:", self.quantity_spin)
        
        layout.addLayout(form_layout)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        self.button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(self.button_box)
    
    def populate_form(self):
        if not self.product_data:
            return
        
        self.barcode_edit.setText(self.product_data["barcode"])
        self.name_edit.setText(self.product_data["name"])
        
        # تحديد الفئة
        index = self.category_combo.findText(self.product_data["category"])
        if index >= 0:
            self.category_combo.setCurrentIndex(index)
        
        self.purchase_price_spin.setValue(self.product_data["purchase_price"])
        self.selling_price_spin.setValue(self.product_data["selling_price"])
        self.quantity_spin.setValue(self.product_data["quantity"])
    
    def get_product_data(self):
        return {
            "barcode": self.barcode_edit.text(),
            "name": self.name_edit.text(),
            "category": self.category_combo.currentText(),
            "purchase_price": self.purchase_price_spin.value(),
            "selling_price": self.selling_price_spin.value(),
            "quantity": self.quantity_spin.value()
        }

class InventoryPage(QWidget):
    """
    واجهة قسم المخزون
    """
    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.products = []  # تهيئة قائمة المنتجات
        self.invoices = []  # تهيئة قائمة الفواتير
        self.categories = []  # تهيئة قائمة الفئات
        self.setup_ui()
        self.load_data()  # تحميل البيانات من قاعدة البيانات
    
    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        
        # إنشاء تبويبات
        self.tabs = QTabWidget()
        
        # تبويب المنتجات
        products_tab = QWidget()
        self.setup_products_tab(products_tab)
        self.tabs.addTab(products_tab, "المنتجات")
        
        # تبويب فواتير الشراء
        invoices_tab = QWidget()
        self.setup_invoices_tab(invoices_tab)
        self.tabs.addTab(invoices_tab, "فواتير الشراء")
        
        # تبويب الفئات
        categories_tab = QWidget()
        self.setup_categories_tab(categories_tab)
        self.tabs.addTab(categories_tab, "الفئات")
        
        main_layout.addWidget(self.tabs)
    
    def setup_products_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.product_search_edit = QLineEdit()
        self.product_search_edit.setPlaceholderText("اسم المنتج أو الباركود")
        self.product_search_edit.textChanged.connect(self.filter_products)
        search_layout.addWidget(self.product_search_edit)
        search_layout.addWidget(search_label)
        layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_product_button = QPushButton("إضافة منتج")
        self.edit_product_button = QPushButton("تعديل منتج")
        self.delete_product_button = QPushButton("حذف منتج")
        
        self.add_product_button.clicked.connect(self.add_product)
        self.edit_product_button.clicked.connect(self.edit_product)
        self.delete_product_button.clicked.connect(self.delete_product)
        
        actions_layout.addWidget(self.add_product_button)
        actions_layout.addWidget(self.edit_product_button)
        actions_layout.addWidget(self.delete_product_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 3. جدول المنتجات
        self.products_table = QTableView()
        self.products_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.products_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.products_model = QStandardItemModel()
        self.products_model.setHorizontalHeaderLabels(["الباركود", "اسم المنتج", "الفئة", "سعر الشراء", "سعر البيع", "الكمية"])
        self.products_table.setModel(self.products_model)
        
        layout.addWidget(self.products_table)
    
    def setup_invoices_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.invoice_search_edit = QLineEdit()
        self.invoice_search_edit.setPlaceholderText("رقم الفاتورة أو اسم المورد")
        self.invoice_search_edit.textChanged.connect(self.filter_invoices)
        search_layout.addWidget(self.invoice_search_edit)
        search_layout.addWidget(search_label)
        layout.addLayout(search_layout)
        
        # 2. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_invoice_button = QPushButton("إضافة فاتورة")
        self.view_invoice_button = QPushButton("عرض فاتورة")
        self.delete_invoice_button = QPushButton("حذف فاتورة")
        
        self.add_invoice_button.clicked.connect(self.add_invoice)
        self.view_invoice_button.clicked.connect(self.view_invoice)
        self.delete_invoice_button.clicked.connect(self.delete_invoice)
        
        actions_layout.addWidget(self.add_invoice_button)
        actions_layout.addWidget(self.view_invoice_button)
        actions_layout.addWidget(self.delete_invoice_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 3. جدول الفواتير
        self.invoices_table = QTableView()
        self.invoices_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.invoices_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.invoices_model = QStandardItemModel()
        self.invoices_model.setHorizontalHeaderLabels(["رقم الفاتورة", "المورد", "التاريخ", "المبلغ الإجمالي", "المدفوع", "المتبقي"])
        self.invoices_table.setModel(self.invoices_model)
        
        layout.addWidget(self.invoices_table)
    
    def setup_categories_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 1. شريط الإجراءات
        actions_layout = QHBoxLayout()
        self.add_category_button = QPushButton("إضافة فئة")
        self.edit_category_button = QPushButton("تعديل فئة")
        self.delete_category_button = QPushButton("حذف فئة")
        
        self.add_category_button.clicked.connect(self.add_category)
        self.edit_category_button.clicked.connect(self.edit_category)
        self.delete_category_button.clicked.connect(self.delete_category)
        
        actions_layout.addWidget(self.add_category_button)
        actions_layout.addWidget(self.edit_category_button)
        actions_layout.addWidget(self.delete_category_button)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # 2. جدول الفئات
        self.categories_table = QTableView()
        self.categories_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.categories_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.categories_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        self.categories_model = QStandardItemModel()
        self.categories_model.setHorizontalHeaderLabels(["المعرف", "اسم الفئة", "الفئة الأب"])
        self.categories_table.setModel(self.categories_model)
        
        layout.addWidget(self.categories_table)
    
    def display_products(self, products=None):
        """
        عرض المنتجات في الجدول
        """
        self.products_model.clear()
        self.products_model.setHorizontalHeaderLabels(["الباركود", "اسم المنتج", "الفئة", "سعر الشراء", "سعر البيع", "الكمية"])
        
        if products is None:
            products = self.products
        
        for product in products:
            row = []
            row.append(QStandardItem(product["barcode"]))
            row.append(QStandardItem(product["name"]))
            row.append(QStandardItem(product["category"]))
            row.append(QStandardItem(str(product["purchase_price"])))
            row.append(QStandardItem(str(product["selling_price"])))
            row.append(QStandardItem(str(product["quantity"])))
            
            self.products_model.appendRow(row)
    
    def display_invoices(self, invoices=None):
        """
        عرض الفواتير في الجدول
        """
        self.invoices_model.clear()
        self.invoices_model.setHorizontalHeaderLabels(["رقم الفاتورة", "المورد", "التاريخ", "المبلغ الإجمالي", "المدفوع", "المتبقي"])
        
        if invoices is None:
            invoices = self.invoices
        
        for invoice in invoices:
            row = []
            row.append(QStandardItem(invoice["number"]))
            row.append(QStandardItem(invoice["supplier"]))
            row.append(QStandardItem(invoice["date"]))
            row.append(QStandardItem(str(invoice["total"])))
            row.append(QStandardItem(str(invoice["paid"])))
            row.append(QStandardItem(str(invoice["remaining"])))
            
            self.invoices_model.appendRow(row)
    
    def display_categories(self, categories=None):
        """
        عرض الفئات في الجدول
        """
        self.categories_model.clear()
        self.categories_model.setHorizontalHeaderLabels(["المعرف", "اسم الفئة", "الفئة الأب"])
        
        if categories is None:
            categories = self.categories
        
        for category in categories:
            row = []
            row.append(QStandardItem(str(category["id"])))
            row.append(QStandardItem(category["name"]))
            row.append(QStandardItem(category["parent"]))
            
            self.categories_model.appendRow(row)
    
    def filter_products(self):
        """
        تصفية المنتجات حسب البحث
        """
        search_text = self.product_search_edit.text().lower()
        
        if not search_text:
            self.display_products()
            return
        
        filtered_products = [
            product for product in self.products
            if search_text in product["name"].lower() or search_text in product["barcode"].lower()
        ]
        
        self.display_products(filtered_products)
    
    def filter_invoices(self):
        """
        تصفية الفواتير حسب البحث
        """
        search_text = self.invoice_search_edit.text().lower()
        
        if not search_text:
            self.display_invoices()
            return
        
        filtered_invoices = [
            invoice for invoice in self.invoices
            if search_text in invoice["number"].lower() or search_text in invoice["supplier"].lower()
        ]
        
        self.display_invoices(filtered_invoices)
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        if self.db:
            try:
                # تحميل الفئات
                categories = self.db.execute("SELECT * FROM الفئات").fetchall()
                self.categories = [dict(category) for category in categories]

                # تحميل المنتجات
                products = self.db.execute("""
                    SELECT p.*
                    FROM المنتجات p
                """).fetchall()
                self.products = [dict(product) for product in products]

                # تحميل فواتير الشراء
                invoices = self.db.execute("""
                    SELECT i.*, s.الاسم as supplier_name
                    FROM فواتير_الموردين i
                    LEFT JOIN الموردين s ON s.المعرف = i.المورد_المعرف
                """).fetchall()
                self.invoices = [dict(invoice) for invoice in invoices]
                
                # عرض البيانات
                self.display_categories()
                self.display_products()
                self.display_invoices()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}", QMessageBox.StandardButton.Ok)
        else:
            # بيانات تجريبية للعرض فقط
            self.categories = [
                {"id": 1, "name": "فئة 1", "parent": None},
                {"id": 2, "name": "فئة 2", "parent": None},
                {"id": 3, "name": "فئة 3", "parent": None}
            ]
            self.products = [
                {
                    "barcode": "123456",
                    "name": "منتج 1",
                    "category": "فئة 1",
                    "purchase_price": 100.0,
                    "selling_price": 150.0,
                    "quantity": 10
                }
            ]
            self.invoices = [
                {
                    "number": "INV-001",
                    "supplier": "مورد 1",
                    "date": "2024-01-01",
                    "total": 1000.0,
                    "paid": 500.0,
                    "remaining": 500.0
                }
            ]
            
            # عرض البيانات التجريبية
            self.display_categories()
            self.display_products()
            self.display_invoices()
    
    def add_product(self):
        """
        إضافة منتج جديد
        """
        dialog = AddProductDialog(parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            new_product = dialog.get_product_data()
            self.products.append(new_product)
            self.display_products()
            QMessageBox.information(self, "نجاح", "تم إضافة المنتج بنجاح", QMessageBox.StandardButton.Ok)
    
    def edit_product(self):
        """
        تعديل بيانات المنتج المحدد
        """
        selected_rows = self.products_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد منتج لتعديله", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        barcode = self.products_model.item(row_index, 0).text()
        
        # البحث عن المنتج في القائمة
        product = next((p for p in self.products if p["barcode"] == barcode), None)
        if not product:
            return
        
        dialog = AddProductDialog(product, parent=self)
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_product_data()
            
            # تحديث بيانات المنتج
            for key, value in updated_data.items():
                product[key] = value
            
            self.display_products()
            QMessageBox.information(self, "نجاح", "تم تعديل بيانات المنتج بنجاح", QMessageBox.StandardButton.Ok)
    
    def delete_product(self):
        """
        حذف المنتج المحدد
        """
        selected_rows = self.products_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد منتج لحذفه", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        barcode = self.products_model.item(row_index, 0).text()
        name = self.products_model.item(row_index, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            f"هل أنت متأكد من حذف المنتج {name}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف المنتج من القائمة
            self.products = [p for p in self.products if p["barcode"] != barcode]
            self.display_products()
            QMessageBox.information(self, "نجاح", "تم حذف المنتج بنجاح", QMessageBox.StandardButton.Ok)
    
    def add_invoice(self):
        """
        إضافة فاتورة شراء جديدة
        """
        QMessageBox.information(self, "تنبيه", "هذه الميزة قيد التطوير", QMessageBox.StandardButton.Ok)
    
    def view_invoice(self):
        """
        عرض تفاصيل الفاتورة المحددة
        """
        selected_rows = self.invoices_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد فاتورة لعرضها", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        invoice_number = self.invoices_model.item(row_index, 0).text()
        
        # البحث عن الفاتورة في القائمة
        invoice = next((i for i in self.invoices if i["number"] == invoice_number), None)
        if not invoice:
            return
        
        message = f"""
        رقم الفاتورة: {invoice["number"]}
        المورد: {invoice["supplier"]}
        التاريخ: {invoice["date"]}
        المبلغ الإجمالي: {invoice["total"]}
        المبلغ المدفوع: {invoice["paid"]}
        المبلغ المتبقي: {invoice["remaining"]}
        """
        
        QMessageBox.information(self, "تفاصيل الفاتورة", message, QMessageBox.StandardButton.Ok)
    
    def delete_invoice(self):
        """
        حذف الفاتورة المحددة
        """
        selected_rows = self.invoices_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد فاتورة لحذفها", QMessageBox.StandardButton.Ok)
            return
        
        row_index = selected_rows[0].row()
        invoice_number = self.invoices_model.item(row_index, 0).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الفاتورة {invoice_number}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف الفاتورة من القائمة
            self.invoices = [i for i in self.invoices if i["number"] != invoice_number]
            self.display_invoices()
            QMessageBox.information(self, "نجاح", "تم حذف الفاتورة بنجاح", QMessageBox.StandardButton.Ok)
    
    def add_category(self):
        """
        إضافة فئة جديدة
        """
        QMessageBox.information(self, "تنبيه", "هذه الميزة قيد التطوير", QMessageBox.StandardButton.Ok)
    
    def edit_category(self):
        """
        تعديل الفئة المحددة
        """
        QMessageBox.information(self, "تنبيه", "هذه الميزة قيد التطوير", QMessageBox.StandardButton.Ok)
    
    def delete_category(self):
        """
        حذف الفئة المحددة
        """
        QMessageBox.information(self, "تنبيه", "هذه الميزة قيد التطوير", QMessageBox.StandardButton.Ok) 