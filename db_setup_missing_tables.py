from database import Database

def create_missing_tables():
    """
    إنشاء الجداول الناقصة في قاعدة البيانات
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        db = Database()
        
        # إنشاء جدول البنوك
        db.execute("""
        CREATE TABLE IF NOT EXISTS البنوك (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الاسم VARCHAR(255) NOT NULL,
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول فروع البنوك
        db.execute("""
        CREATE TABLE IF NOT EXISTS فروع_البنوك (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            البنك_الرئيسي_المعرف INT NOT NULL,
            الاسم VARCHAR(255) NOT NULL,
            العنوان VARCHAR(255),
            رقم_الحساب VARCHAR(50),
            رقم_IBAN VARCHAR(50),
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (البنك_الرئيسي_المعرف) REFERENCES البنوك (المعرف)
                ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول فئات المصروفات
        db.execute("""
        CREATE TABLE IF NOT EXISTS فئات_المصروفات (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الاسم VARCHAR(255) NOT NULL UNIQUE,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول المصروفات
        db.execute("""
        CREATE TABLE IF NOT EXISTS المصروفات (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            التاريخ DATE NOT NULL,
            الفئة_المعرف INT NOT NULL,
            المبلغ DECIMAL(10,2) NOT NULL,
            الوصف VARCHAR(255) NOT NULL,
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (الفئة_المعرف) REFERENCES فئات_المصروفات (المعرف)
                ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول صلاحيات النظام
        db.execute("""
        CREATE TABLE IF NOT EXISTS الصلاحيات (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الاسم VARCHAR(100) NOT NULL,
            الوصف VARCHAR(255) NOT NULL,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول صلاحيات الموظفين
        db.execute("""
        CREATE TABLE IF NOT EXISTS صلاحيات_الموظفين (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الموظف_المعرف INT NOT NULL,
            الصلاحية_المعرف INT NOT NULL,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (الموظف_المعرف, الصلاحية_المعرف),
            FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
                ON DELETE CASCADE,
            FOREIGN KEY (الصلاحية_المعرف) REFERENCES الصلاحيات (المعرف)
                ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول الحضور والانصراف
        db.execute("""
        CREATE TABLE IF NOT EXISTS الحضور (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الموظف_المعرف INT NOT NULL,
            وقت_الدخول DATETIME NOT NULL,
            وقت_الخروج DATETIME,
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
                ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول الرواتب
        db.execute("""
        CREATE TABLE IF NOT EXISTS الرواتب (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            الموظف_المعرف INT NOT NULL,
            الشهر TINYINT NOT NULL,
            السنة SMALLINT NOT NULL,
            الراتب_الأساسي DECIMAL(10,2) NOT NULL,
            العمولة DECIMAL(10,2) DEFAULT 0,
            الخصومات DECIMAL(10,2) DEFAULT 0,
            الإضافات DECIMAL(10,2) DEFAULT 0,
            تاريخ_الدفع DATETIME,
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (الموظف_المعرف, الشهر, السنة),
            FOREIGN KEY (الموظف_المعرف) REFERENCES الموظفين (المعرف)
                ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول العقود
        db.execute("""
        CREATE TABLE IF NOT EXISTS العقود (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            رقم_العقد VARCHAR(100) NOT NULL UNIQUE,
            العميل_المعرف INT NOT NULL,
            فرع_البنك_المعرف INT NOT NULL,
            الفاتورة_المعرف INT NOT NULL,
            إجمالي_المبلغ DECIMAL(10,2) NOT NULL,
            مبلغ_القسط DECIMAL(10,2) NOT NULL,
            عدد_الأقساط INT NOT NULL,
            تاريخ_البداية DATE NOT NULL,
            تاريخ_النهاية DATE NOT NULL,
            الحالة ENUM('نشط', 'منتهي', 'ملغي') NOT NULL DEFAULT 'نشط',
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (العميل_المعرف) REFERENCES العملاء (المعرف)
                ON DELETE RESTRICT,
            FOREIGN KEY (فرع_البنك_المعرف) REFERENCES فروع_البنوك (المعرف)
                ON DELETE RESTRICT,
            FOREIGN KEY (الفاتورة_المعرف) REFERENCES الفواتير (المعرف)
                ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # إنشاء جدول الأقساط
        db.execute("""
        CREATE TABLE IF NOT EXISTS الأقساط (
            المعرف INT AUTO_INCREMENT PRIMARY KEY,
            العقد_المعرف INT NOT NULL,
            رقم_القسط INT NOT NULL,
            تاريخ_الاستحقاق DATE NOT NULL,
            المبلغ DECIMAL(10,2) NOT NULL,
            المبلغ_المدفوع DECIMAL(10,2) DEFAULT 0,
            تاريخ_الدفع DATE,
            الحالة ENUM('مستحق', 'مدفوع', 'متأخر') NOT NULL DEFAULT 'مستحق',
            ملاحظات TEXT,
            تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY (العقد_المعرف, رقم_القسط),
            FOREIGN KEY (العقد_المعرف) REFERENCES العقود (المعرف)
                ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        db.commit()
        
        # تحديث جدول العملاء لإضافة الحقول الخاصة بالبنوك
        try:
            db.execute("ALTER TABLE العملاء ADD COLUMN إثبات_الشخصية VARCHAR(100) AFTER العنوان")
            db.commit()
        except Exception:
            print("حقل إثبات_الشخصية موجود مسبقاً في جدول العملاء")

        try:
            db.execute("ALTER TABLE العملاء ADD COLUMN البنك VARCHAR(255) AFTER إثبات_الشخصية")
            db.commit()
        except Exception:
            print("حقل البنك موجود مسبقاً في جدول العملاء")

        try:
            db.execute("ALTER TABLE العملاء ADD COLUMN رقم_الحساب VARCHAR(100) AFTER البنك")
            db.commit()
        except Exception:
            print("حقل رقم_الحساب موجود مسبقاً في جدول العملاء")
        
        # تحديث جدول الموظفين لإضافة حقول إضافية
        try:
            db.execute("ALTER TABLE الموظفين ADD COLUMN تاريخ_التوظيف DATE AFTER العنوان")
            db.commit()
        except Exception:
            print("حقل تاريخ_التوظيف موجود مسبقاً في جدول الموظفين")

        try:
            db.execute("ALTER TABLE الموظفين ADD COLUMN الراتب DECIMAL(10,2) DEFAULT 0 AFTER تاريخ_التوظيف")
            db.commit()
        except Exception:
            print("حقل الراتب موجود مسبقاً في جدول الموظفين")

        try:
            db.execute("ALTER TABLE الموظفين ADD COLUMN الحالة ENUM('نشط', 'غير نشط') DEFAULT 'نشط' AFTER الراتب")
            db.commit()
        except Exception:
            print("حقل الحالة موجود مسبقاً في جدول الموظفين")
        
        # إضافة حقل يتتبع رقم الفاتورة لتسهيل العرض
        try:
            db.execute("ALTER TABLE الفواتير ADD COLUMN رقم_الفاتورة VARCHAR(50) AFTER المعرف")
            db.commit()
        except Exception:
            print("حقل رقم_الفاتورة موجود مسبقاً في جدول الفواتير")

        try:
            db.execute("ALTER TABLE الفواتير ADD UNIQUE INDEX (رقم_الفاتورة)")
            db.commit()
        except Exception:
            print("مؤشر رقم_الفاتورة موجود مسبقاً في جدول الفواتير")
        
        # إدخال بيانات أساسية لجدول الصلاحيات
        permissions = [
            ('إدارة المبيعات', 'صلاحية إدارة المبيعات ونقاط البيع'),
            ('إدارة المخزون', 'صلاحية إدارة المخزون والمنتجات'),
            ('إدارة العملاء', 'صلاحية إدارة بيانات العملاء'),
            ('إدارة الموردين', 'صلاحية إدارة بيانات الموردين'),
            ('إدارة الموظفين', 'صلاحية إدارة بيانات الموظفين'),
            ('إدارة المصروفات', 'صلاحية إدارة المصروفات'),
            ('إدارة البنوك', 'صلاحية إدارة البنوك وفروعها'),
            ('إدارة العقود', 'صلاحية إدارة العقود والأقساط'),
            ('إدارة التقارير', 'صلاحية عرض وطباعة التقارير'),
            ('إعدادات النظام', 'صلاحية التحكم بإعدادات النظام')
        ]
        
        for name, description in permissions:
            try:
                db.execute(
                    "INSERT INTO الصلاحيات (الاسم, الوصف) VALUES (%s, %s)",
                    (name, description)
                )
                db.commit()
            except Exception:
                print(f"الصلاحية '{name}' موجودة مسبقاً")
                
        print("تم إنشاء الجداول الناقصة بنجاح")
        
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء الجداول الناقصة: {str(e)}")
    finally:
        if 'db' in locals():
            db.close()

if __name__ == '__main__':
    create_missing_tables() 