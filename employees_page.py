from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                               QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                               QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit,
                               QMessageBox, QTabWidget)
from PySide6.QtCore import Qt, QDate
from database import Database
from add_employee_dialog import AddEmployeeDialog

class EmployeesPage(QWidget):
    """صفحة إدارة الموظفين
    تتيح إدارة بيانات الموظفين والرواتب والحضور والصلاحيات
    """
    def __init__(self, db=None):
        super().__init__()
        if not db:
            from database import Database
            self.db = Database()
        else:
            self.db = db
        self.setup_ui()
        self.load_employees()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # إضافة تبويبات رئيسية
        self.employees_tab = QWidget()
        self.attendance_tab = QWidget()
        self.salaries_tab = QWidget()
        self.permissions_tab = QWidget()
        
        self.setup_employees_tab()
        self.setup_attendance_tab()
        self.setup_salaries_tab()
        self.setup_permissions_tab()
        
        self.tabs.addTab(self.employees_tab, "الموظفين")
        self.tabs.addTab(self.attendance_tab, "الحضور والانصراف")
        self.tabs.addTab(self.salaries_tab, "الرواتب")
        self.tabs.addTab(self.permissions_tab, "الصلاحيات")
        
        main_layout.addWidget(self.tabs)
        
    def setup_employees_tab(self):
        """إعداد تبويب بيانات الموظفين"""
        layout = QVBoxLayout(self.employees_tab)
        
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.setMaximumWidth(200)
        self.search_input.textChanged.connect(self.filter_employees)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط"])
        self.status_filter.currentTextChanged.connect(self.filter_employees)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        self.add_btn = QPushButton("إضافة موظف")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        
        self.add_btn.clicked.connect(self.add_employee)
        self.edit_btn.clicked.connect(self.edit_employee)
        self.delete_btn.clicked.connect(self.delete_employee)
        
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addStretch()
        
        # جدول الموظفين
        self.employees_table = QTableWidget()
        self.employees_table.setColumnCount(7)
        self.employees_table.setHorizontalHeaderLabels([
            "رقم الموظف", "الاسم", "رقم الهاتف", "العنوان",
            "تاريخ التوظيف", "الراتب", "الحالة"
        ])
        
        layout.addLayout(search_layout)
        layout.addLayout(actions_layout)
        layout.addWidget(self.employees_table)
        
    def setup_attendance_tab(self):
        """إعداد تبويب الحضور والانصراف"""
        layout = QVBoxLayout(self.attendance_tab)
        
        # التاريخ والفلترة
        date_layout = QHBoxLayout()
        self.attendance_date = QDateEdit()
        self.attendance_date.setDate(QDate.currentDate())
        self.attendance_date.dateChanged.connect(self.load_attendance)
        
        date_layout.addWidget(QLabel("التاريخ:"))
        date_layout.addWidget(self.attendance_date)
        date_layout.addStretch()
        
        # أزرار تسجيل الحضور والانصراف
        buttons_layout = QHBoxLayout()
        self.check_in_btn = QPushButton("تسجيل حضور")
        self.check_out_btn = QPushButton("تسجيل انصراف")
        
        self.check_in_btn.clicked.connect(lambda: self.record_attendance("in"))
        self.check_out_btn.clicked.connect(lambda: self.record_attendance("out"))
        
        buttons_layout.addWidget(self.check_in_btn)
        buttons_layout.addWidget(self.check_out_btn)
        buttons_layout.addStretch()
        
        # جدول الحضور
        self.attendance_table = QTableWidget()
        self.attendance_table.setColumnCount(5)
        self.attendance_table.setHorizontalHeaderLabels([
            "رقم الموظف", "الاسم", "وقت الحضور", "وقت الانصراف", "ملاحظات"
        ])
        
        layout.addLayout(date_layout)
        layout.addLayout(buttons_layout)
        layout.addWidget(self.attendance_table)
        
    def setup_salaries_tab(self):
        """إعداد تبويب الرواتب"""
        layout = QVBoxLayout(self.salaries_tab)
        
        # فلترة الشهر والسنة
        filter_layout = QHBoxLayout()
        self.month_combo = QComboBox()
        self.month_combo.addItems([
            "يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ])
        self.month_combo.setCurrentIndex(QDate.currentDate().month() - 1)
        self.month_combo.currentIndexChanged.connect(self.load_salaries)
        
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2100)
        self.year_spin.setValue(QDate.currentDate().year())
        self.year_spin.valueChanged.connect(self.load_salaries)
        
        filter_layout.addWidget(QLabel("الشهر:"))
        filter_layout.addWidget(self.month_combo)
        filter_layout.addWidget(QLabel("السنة:"))
        filter_layout.addWidget(self.year_spin)
        filter_layout.addStretch()
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        self.calculate_btn = QPushButton("احتساب الرواتب")
        self.pay_btn = QPushButton("صرف الرواتب")
        
        self.calculate_btn.clicked.connect(self.calculate_salaries)
        self.pay_btn.clicked.connect(self.pay_salaries)
        
        actions_layout.addWidget(self.calculate_btn)
        actions_layout.addWidget(self.pay_btn)
        actions_layout.addStretch()
        
        # جدول الرواتب
        self.salaries_table = QTableWidget()
        self.salaries_table.setColumnCount(8)
        self.salaries_table.setHorizontalHeaderLabels([
            "رقم الموظف", "الاسم", "الراتب الأساسي", "العمولة",
            "الخصومات", "الإضافات", "الصافي", "الحالة"
        ])
        
        layout.addLayout(filter_layout)
        layout.addLayout(actions_layout)
        layout.addWidget(self.salaries_table)
        
    def setup_permissions_tab(self):
        """إعداد تبويب الصلاحيات"""
        layout = QVBoxLayout(self.permissions_tab)
        
        # اختيار الموظف
        employee_layout = QHBoxLayout()
        self.employee_combo = QComboBox()
        self.employee_combo.currentIndexChanged.connect(self.load_permissions)
        
        # زر حفظ الصلاحيات
        self.save_permissions_btn = QPushButton("حفظ الصلاحيات")
        self.save_permissions_btn.clicked.connect(self.save_permissions)
        
        employee_layout.addWidget(QLabel("الموظف:"))
        employee_layout.addWidget(self.employee_combo)
        employee_layout.addWidget(self.save_permissions_btn)
        employee_layout.addStretch()
        
        # قائمة الصلاحيات
        self.permissions_table = QTableWidget()
        self.permissions_table.setColumnCount(2)
        self.permissions_table.setHorizontalHeaderLabels([
            "الصلاحية", "مسموح"
        ])
        
        layout.addLayout(employee_layout)
        layout.addWidget(self.permissions_table)
        
    def add_employee(self):
        """إضافة موظف جديد"""
        dialog = AddEmployeeDialog(self)
        if dialog.exec():
            employee_data = dialog.get_employee_data()
            try:
                # إضافة الموظف لقاعدة البيانات
                self.db.execute("""
                    INSERT INTO الموظفين (الاسم, الهاتف, العنوان, تاريخ_التوظيف, الراتب, الحالة)
                    VALUES (%s, %s, %s, ?, ?, ?)
                """, (
                    employee_data['name'],
                    employee_data['phone'],
                    employee_data['address'],
                    employee_data['hire_date'],
                    employee_data['salary'],
                    employee_data['status']
                ))
                self.db.commit()
                self.load_employees()
                QMessageBox.information(self, "نجاح", "تم إضافة الموظف بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الموظف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def edit_employee(self):
        """تعديل بيانات موظف"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار موظف للتعديل", QMessageBox.StandardButton.Ok)
            return
            
        current_item = self.employees_table.item(current_row, 0)
        if not current_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد الموظف", QMessageBox.StandardButton.Ok)
            return
            
        employee_id = int(current_item.text())
        self.db.execute("""
            SELECT الاسم, الهاتف, العنوان, تاريخ_التوظيف, الراتب, الحالة
            FROM الموظفين WHERE المعرف = %s
        """, (employee_id,))
        employee_data = self.db.fetchone()
        
        if not employee_data:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على بيانات الموظف", QMessageBox.StandardButton.Ok)
            return
            
        dialog = AddEmployeeDialog(self, {
            'name': employee_data['الاسم'],
            'phone': employee_data['الهاتف'],
            'address': employee_data['العنوان'],
            'hire_date': employee_data['تاريخ_التوظيف'],
            'salary': employee_data['الراتب'],
            'status': employee_data['الحالة']
        })
        
        if dialog.exec():
            updated_data = dialog.get_employee_data()
            try:
                self.db.execute("""
                    UPDATE الموظفين
                    SET الاسم = %s, الهاتف = %s, العنوان = %s, تاريخ_التوظيف = %s,
                        الراتب = %s, الحالة = %s
                    WHERE المعرف = %s
                """, (
                    updated_data['name'],
                    updated_data['phone'],
                    updated_data['address'],
                    updated_data['hire_date'],
                    updated_data['salary'],
                    updated_data['status'],
                    employee_id
                ))
                self.db.commit()
                self.load_employees()
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات الموظف بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث بيانات الموظف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def delete_employee(self):
        """حذف موظف"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار موظف للحذف", QMessageBox.StandardButton.Ok)
            return
            
        current_item = self.employees_table.item(current_row, 0)
        name_item = self.employees_table.item(current_row, 1)
        if not current_item or not name_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد الموظف", QMessageBox.StandardButton.Ok)
            return
            
        employee_id = int(current_item.text())
        employee_name = name_item.text()
        
        # التحقق من وجود رواتب مدفوعة
        self.db.execute("""
            SELECT COUNT(*) as count FROM الرواتب WHERE الموظف_المعرف = %s
        """, (employee_id,))
        result = self.db.fetchone()
        has_salaries = result['count'] if result else 0
        
        if has_salaries:
            QMessageBox.warning(
                self,
                "تعذر الحذف",
                "لا يمكن حذف الموظف لوجود سجلات رواتب مرتبطة به",
                QMessageBox.StandardButton.Ok
            )
            return
            
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الموظف {employee_name}؟\nسيتم حذف جميع سجلات الحضور والصلاحيات المرتبطة به.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # حذف سجلات الحضور والصلاحيات أولاً (CASCADE)
                self.db.execute("DELETE FROM الموظفين WHERE المعرف = %s", (employee_id,))
                self.db.commit()
                self.load_employees()
                QMessageBox.information(self, "نجاح", "تم حذف الموظف بنجاح", QMessageBox.StandardButton.Ok)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الموظف: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def filter_employees(self):
        """تصفية قائمة الموظفين"""
        search_text = self.search_input.text().strip()
        status_filter = self.status_filter.currentText()
        
        query = "SELECT المعرف, الاسم, الهاتف, العنوان, تاريخ_التوظيف, الراتب, الحالة FROM الموظفين WHERE 1=1"
        params = []

        if search_text:
            query += " AND (الاسم LIKE %s OR الهاتف LIKE %s OR العنوان LIKE %s)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        if status_filter != "الكل":
            query += " AND الحالة = %s"
            params.append(status_filter)
            
        try:
            self.db.execute(query, params)
            employees = self.db.fetchall()
            self.employees_table.setRowCount(len(employees))
            
            for row, employee in enumerate(employees):
                for col, key in enumerate(['id', 'name', 'phone', 'address', 'hire_date', 'salary', 'status']):
                    item = QTableWidgetItem(str(employee[key]))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.employees_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
        
    def load_employees(self):
        """تحميل وعرض بيانات الموظفين"""
        try:
            # تنفيذ الاستعلام
            self.db.execute("""
                SELECT المعرف, الاسم, الهاتف, العنوان, تاريخ_التوظيف, الراتب, الحالة
                FROM الموظفين
                ORDER BY الاسم
            """)
            employees = self.db.fetchall()
            
            # تحديث الجدول
            self.employees_table.setRowCount(len(employees))
            for row, employee in enumerate(employees):
                self.employees_table.setItem(row, 0, QTableWidgetItem(str(employee['المعرف'])))  # رقم الموظف
                self.employees_table.setItem(row, 1, QTableWidgetItem(employee['الاسم']))       # الاسم
                self.employees_table.setItem(row, 2, QTableWidgetItem(employee['الهاتف']))       # رقم الهاتف
                self.employees_table.setItem(row, 3, QTableWidgetItem(employee['العنوان']))       # العنوان
                self.employees_table.setItem(row, 4, QTableWidgetItem(employee['تاريخ_التوظيف']))       # تاريخ التوظيف
                self.employees_table.setItem(row, 5, QTableWidgetItem(str(employee['الراتب'])))  # الراتب
                self.employees_table.setItem(row, 6, QTableWidgetItem(employee['الحالة']))       # الحالة
                
            # تحديث قائمة الموظفين في تبويب الصلاحيات
            self.employee_combo.clear()
            self.employee_combo.addItem("- اختر موظف -", None)
            for employee in employees:
                self.employee_combo.addItem(employee['الاسم'], employee['المعرف'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}", QMessageBox.StandardButton.Ok)
        
    def load_attendance(self):
        """تحميل وعرض سجلات الحضور والانصراف"""
        try:
            date = self.attendance_date.date().toString('yyyy-MM-dd')
            
            # تنفيذ الاستعلام
            self.db.execute("""
                SELECT a.الموظف_المعرف, e.الاسم, a.وقت_الدخول, a.وقت_الخروج, a.ملاحظات
                FROM الحضور a
                JOIN الموظفين e ON e.المعرف = a.الموظف_المعرف
                WHERE DATE(a.وقت_الدخول) = %s
                ORDER BY a.وقت_الدخول
            """, (date,))
            attendance = self.db.fetchall()
            
            # تحديث الجدول
            self.attendance_table.setRowCount(len(attendance))
            for row, record in enumerate(attendance):
                self.attendance_table.setItem(row, 0, QTableWidgetItem(str(record['الموظف_المعرف'])))  # رقم الموظف
                self.attendance_table.setItem(row, 1, QTableWidgetItem(record['الاسم']))       # اسم الموظف
                self.attendance_table.setItem(row, 2, QTableWidgetItem(record['وقت_الدخول']))       # وقت الحضور
                self.attendance_table.setItem(row, 3, QTableWidgetItem(record['وقت_الخروج'] if record['وقت_الخروج'] else ""))  # وقت الانصراف
                self.attendance_table.setItem(row, 4, QTableWidgetItem(record['ملاحظات'] if record['ملاحظات'] else ""))  # ملاحظات
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل سجلات الحضور: {str(e)}", QMessageBox.StandardButton.Ok)
            
    def record_attendance(self, check_type="in"):
        """تسجيل حضور أو انصراف موظف"""
        current_row = self.employees_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار موظف لتسجيل الحضور/الانصراف", QMessageBox.StandardButton.Ok)
            return
            
        id_item = self.employees_table.item(current_row, 0)
        if not id_item:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ في تحديد الموظف", QMessageBox.StandardButton.Ok)
            return
            
        employee_id = int(id_item.text())
        current_date = QDate.currentDate().toString('yyyy-MM-dd')
        
        try:
            if check_type == "in":
                # التحقق من عدم وجود تسجيل حضور سابق لنفس اليوم
                self.db.execute("""
                    SELECT المعرف FROM الحضور
                    WHERE الموظف_المعرف = %s AND DATE(وقت_الدخول) = %s
                """, (employee_id, current_date))
                
                existing = self.db.fetchone()
                
                if existing:
                    QMessageBox.warning(self, "تنبيه", "تم تسجيل حضور الموظف مسبقاً اليوم", QMessageBox.StandardButton.Ok)
                    return
                    
                # تسجيل الحضور
                self.db.execute("""
                    INSERT INTO الحضور (الموظف_المعرف, وقت_الدخول)
                    VALUES (?, CURRENT_TIMESTAMP)
                """, (employee_id,))
                
            else:  # check_type == "out"
                # التحقق من وجود تسجيل حضور لنفس اليوم
                self.db.execute("""
                    SELECT المعرف, وقت_الخروج FROM الحضور
                    WHERE الموظف_المعرف = %s AND DATE(وقت_الدخول) = %s
                """, (employee_id, current_date))
                
                attendance = self.db.fetchone()
                
                if not attendance:
                    QMessageBox.warning(self, "تنبيه", "لم يتم تسجيل حضور الموظف اليوم", QMessageBox.StandardButton.Ok)
                    return
                    
                if attendance.get('وقت_الخروج'):
                    QMessageBox.warning(self, "تنبيه", "تم تسجيل انصراف الموظف مسبقاً", QMessageBox.StandardButton.Ok)
                    return

                # تسجيل الانصراف
                self.db.execute("""
                    UPDATE الحضور SET وقت_الخروج = CURRENT_TIMESTAMP
                    WHERE المعرف = %s
                """, (attendance['المعرف'],))
                
            self.db.commit()
            self.load_attendance()
            QMessageBox.information(
                self,
                "نجاح",
                "تم تسجيل " + ("الحضور" if check_type == "in" else "الانصراف") + " بنجاح",
                QMessageBox.StandardButton.Ok
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تسجيل " + ("الحضور" if check_type == "in" else "الانصراف") + f": {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        
    def load_permissions(self):
        """تحميل وعرض صلاحيات الموظف"""
        employee_id = self.employee_combo.currentData()
        if not employee_id:
            self.permissions_table.setRowCount(0)
            return
            
        try:
            # تحميل جميع الصلاحيات المتاحة
            self.db.execute("""
                SELECT p.المعرف, p.الاسم, p.الوصف,
                    CASE WHEN ep.الصلاحية_المعرف IS NOT NULL THEN 1 ELSE 0 END as has_permission
                FROM الصلاحيات p
                LEFT JOIN صلاحيات_الموظفين ep ON ep.الصلاحية_المعرف = p.المعرف
                    AND ep.الموظف_المعرف = %s
                ORDER BY p.الاسم
            """, (employee_id,))
            
            permissions = self.db.fetchall()
            
            # تحديث الجدول
            self.permissions_table.setRowCount(len(permissions))
            for row, perm in enumerate(permissions):
                # اسم الصلاحية
                name_item = QTableWidgetItem(perm['الاسم'])
                name_item.setToolTip(perm['الوصف'])  # وصف الصلاحية
                self.permissions_table.setItem(row, 0, name_item)

                # حالة الصلاحية
                checkbox = QTableWidgetItem()
                checkbox.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                checkbox.setCheckState(
                    Qt.CheckState.Checked if perm['has_permission'] else Qt.CheckState.Unchecked
                )
                checkbox.setData(Qt.ItemDataRole.UserRole, perm['المعرف'])  # تخزين معرف الصلاحية
                self.permissions_table.setItem(row, 1, checkbox)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الصلاحيات: {str(e)}", QMessageBox.StandardButton.Ok)
            
    def save_permissions(self):
        """حفظ صلاحيات الموظف"""
        employee_id = self.employee_combo.currentData()
        if not employee_id:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار موظف", QMessageBox.StandardButton.Ok)
            return
            
        try:
            # حذف جميع صلاحيات الموظف الحالية
            self.db.execute("""
                DELETE FROM صلاحيات_الموظفين WHERE الموظف_المعرف = %s
            """, (employee_id,))
            
            # إضافة الصلاحيات المحددة
            for row in range(self.permissions_table.rowCount()):
                checkbox = self.permissions_table.item(row, 1)
                if not checkbox:
                    continue
                    
                if checkbox.checkState() == Qt.CheckState.Checked:
                    permission_id = checkbox.data(Qt.ItemDataRole.UserRole)
                    if permission_id is not None:
                        self.db.execute("""
                            INSERT INTO صلاحيات_الموظفين (الموظف_المعرف, الصلاحية_المعرف)
                            VALUES (%s, %s)
                        """, (employee_id, permission_id))
                    
            self.db.commit()
            QMessageBox.information(self, "نجاح", "تم حفظ الصلاحيات بنجاح", QMessageBox.StandardButton.Ok)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الصلاحيات: {str(e)}", QMessageBox.StandardButton.Ok)

    def load_salaries(self):
        """تحميل وعرض الرواتب"""
        try:
            month = self.month_combo.currentIndex() + 1
            year = self.year_spin.value()
            
            # تحميل الرواتب
            self.db.execute("""
                SELECT s.*, e.الاسم, e.الراتب as base_salary
                FROM الموظفين e
                LEFT JOIN الرواتب s ON s.الموظف_المعرف = e.المعرف
                    AND s.الشهر = %s AND s.السنة = %s
                WHERE e.الحالة = 'نشط'
                ORDER BY e.الاسم
            """, (month, year))
            
            salaries = self.db.fetchall()
            
            # تحديث الجدول
            self.salaries_table.setRowCount(len(salaries))
            for row, salary in enumerate(salaries):
                self.salaries_table.setItem(row, 0, QTableWidgetItem(str(salary['الموظف_المعرف']) if salary.get('الموظف_المعرف') else ''))
                self.salaries_table.setItem(row, 1, QTableWidgetItem(salary['الاسم']))
                self.salaries_table.setItem(row, 2, QTableWidgetItem(str(salary['base_salary'])))
                self.salaries_table.setItem(row, 3, QTableWidgetItem(str(salary['العمولة'] if salary['العمولة'] else 0)))
                self.salaries_table.setItem(row, 4, QTableWidgetItem(str(salary['الخصومات'] if salary['الخصومات'] else 0)))
                self.salaries_table.setItem(row, 5, QTableWidgetItem(str(salary['الإضافات'] if salary['الإضافات'] else 0)))

                # حساب الصافي
                net = salary['base_salary']
                if salary['العمولة']:
                    net += salary['العمولة']
                if salary['الخصومات']:
                    net -= salary['الخصومات']
                if salary['الإضافات']:
                    net += salary['الإضافات']

                self.salaries_table.setItem(row, 6, QTableWidgetItem(str(net)))
                self.salaries_table.setItem(row, 7, QTableWidgetItem("تم الصرف" if salary['المعرف'] else "لم يتم الصرف"))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الرواتب: {str(e)}", QMessageBox.StandardButton.Ok)
            
    def calculate_salaries(self):
        """احتساب الرواتب للشهر الحالي"""
        try:
            month = self.month_combo.currentIndex() + 1
            year = self.year_spin.value()
            
            # التحقق من عدم وجود رواتب محتسبة مسبقاً
            self.db.execute("""
                SELECT COUNT(*) as count FROM الرواتب
                WHERE الشهر = %s AND السنة = %s
            """, (month, year))
            
            result = self.db.fetchone()
            existing = result['count'] if result else 0
            
            if existing > 0:
                reply = QMessageBox.question(
                    self,
                    "تأكيد",
                    "يوجد رواتب محتسبة مسبقاً لهذا الشهر. هل تريد إعادة الاحتساب؟",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                
                if reply == QMessageBox.StandardButton.No:
                    return
                    
                # حذف الرواتب السابقة
                self.db.execute("""
                    DELETE FROM الرواتب
                    WHERE الشهر = %s AND السنة = %s
                """, (month, year))
            
            # احتساب الرواتب لكل موظف نشط
            self.db.execute("""
                SELECT المعرف, الراتب FROM الموظفين
                WHERE الحالة = 'نشط'
            """)
            
            employees = self.db.fetchall() or []
            
            for employee in employees:
                # احتساب العمولة من المبيعات
                self.db.execute("""
                    SELECT COALESCE(SUM(العمولة), 0) as total FROM المبيعات
                    WHERE الموظف_المعرف = %s
                        AND strftime('%m', التاريخ) = %s
                        AND strftime('%Y', التاريخ) = %s
                """, (employee['المعرف'], f"{month:02d}", str(year)))
                
                result = self.db.fetchone()
                commission = result['total'] if result else 0
                
                # احتساب الخصومات من الغياب
                self.db.execute("""
                    SELECT COUNT(*) as count FROM (
                        SELECT DISTINCT DATE(وقت_الدخول) as work_day
                        FROM الحضور
                        WHERE الموظف_المعرف = %s
                            AND strftime('%m', وقت_الدخول) = %s
                            AND strftime('%Y', وقت_الدخول) = %s
                    )
                """, (employee['المعرف'], f"{month:02d}", str(year)))
                
                result = self.db.fetchone()
                absences = result['count'] if result else 0
                
                working_days = 22  # عدد أيام العمل في الشهر
                daily_rate = employee['الراتب'] / working_days
                deductions = (working_days - absences) * daily_rate if absences < working_days else employee['الراتب']

                # إضافة الراتب
                self.db.execute("""
                    INSERT INTO الرواتب (
                        الموظف_المعرف, الشهر, السنة, الراتب_الأساسي,
                        العمولة, الخصومات, الإضافات
                    ) VALUES (%s, %s, %s, ?, ?, ?, 0)
                """, (
                    employee['المعرف'], month, year, employee['الراتب'],
                    commission, deductions
                ))
                
            self.db.commit()
            self.load_salaries()
            QMessageBox.information(self, "نجاح", "تم احتساب الرواتب بنجاح", QMessageBox.StandardButton.Ok)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء احتساب الرواتب: {str(e)}", QMessageBox.StandardButton.Ok)
            
    def pay_salaries(self):
        """صرف الرواتب المحتسبة"""
        try:
            month = self.month_combo.currentIndex() + 1
            year = self.year_spin.value()
            
            # التحقق من وجود رواتب غير مدفوعة
            self.db.execute("""
                SELECT COUNT(*) as count FROM الرواتب
                WHERE الشهر = %s AND السنة = %s
                    AND تاريخ_الدفع IS NULL
            """, (month, year))
            
            result = self.db.fetchone()
            unpaid = result['count'] if result else 0
            
            if unpaid == 0:
                QMessageBox.information(
                    self,
                    "تنبيه",
                    "لا يوجد رواتب غير مدفوعة لهذا الشهر",
                    QMessageBox.StandardButton.Ok
                )
                return
                
            reply = QMessageBox.question(
                self,
                "تأكيد",
                f"هل أنت متأكد من صرف رواتب شهر {self.month_combo.currentText()} {year}؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # تحديث تاريخ الدفع
                self.db.execute("""
                    UPDATE الرواتب
                    SET تاريخ_الدفع = CURRENT_TIMESTAMP
                    WHERE الشهر = %s AND السنة = %s
                        AND تاريخ_الدفع IS NULL
                """, (month, year))
                
                self.db.commit()
                self.load_salaries()
                QMessageBox.information(self, "نجاح", "تم صرف الرواتب بنجاح", QMessageBox.StandardButton.Ok)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء صرف الرواتب: {str(e)}", QMessageBox.StandardButton.Ok)
