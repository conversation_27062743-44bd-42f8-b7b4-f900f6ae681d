#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح استخدامات cursor في جميع ملفات Python
"""

import os
import re

def fix_cursor_usage(file_path):
    """إصلاح استخدامات cursor في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إصلاح النمط: cursor = self.db.cursor()
        # cursor.execute(...)
        # إلى: cursor = self.db.execute(...)
        
        # البحث عن النمط وإصلاحه
        pattern1 = r'(\s+)cursor = self\.db\.cursor\(\)\s*\n(\s+)cursor\.execute\((.*?)\)'
        replacement1 = r'\1cursor = self.db.execute(\3)'
        content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE)
        
        # إزالة cursor.close() المتبقية
        content = re.sub(r'\s*cursor\.close\(\)\s*\n', '\n', content)
        
        # إصلاح استخدام ? إلى %s في MySQL
        content = content.replace('LIKE ?', 'LIKE %s')
        content = content.replace('= ?', '= %s')
        content = content.replace('VALUES (?, ?, ?', 'VALUES (%s, %s, %s')
        content = content.replace('VALUES (?, ?, ?, ?', 'VALUES (%s, %s, %s, %s')
        content = content.replace('VALUES (?, ?, ?, ?, ?', 'VALUES (%s, %s, %s, %s, %s')
        content = content.replace('VALUES (?, ?', 'VALUES (%s, %s')
        content = content.replace('WHERE id = ?', 'WHERE المعرف = %s')
        content = content.replace('WHERE المعرف = ?', 'WHERE المعرف = %s')
        
        # حفظ الملف إذا تم تغييره
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"تم إصلاح: {file_path}")
            return True
        else:
            print(f"لا يحتاج إصلاح: {file_path}")
            return False
            
    except Exception as e:
        print(f"خطأ في إصلاح {file_path}: {str(e)}")
        return False

def main():
    """إصلاح جميع ملفات Python في المجلد الحالي"""
    current_dir = os.getcwd()
    python_files = [f for f in os.listdir(current_dir) if f.endswith('.py') and f != 'إصلاح_cursor.py']
    
    print("بدء إصلاح ملفات Python...")
    print("=" * 40)
    
    fixed_count = 0
    total_count = len(python_files)
    
    for file_name in python_files:
        file_path = os.path.join(current_dir, file_name)
        if fix_cursor_usage(file_path):
            fixed_count += 1
    
    print("=" * 40)
    print(f"تم إصلاح {fixed_count} من {total_count} ملف")
    print("انتهى الإصلاح!")

if __name__ == "__main__":
    main()
