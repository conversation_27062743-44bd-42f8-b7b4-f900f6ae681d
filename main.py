import sys
from database import Database
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QListWidget, QListWidgetItem, QToolBar,
    QSplitter, QFrame, QMessageBox
)
from PySide6.QtGui import QIcon, QAction, QFont
from PySide6.QtCore import Qt, QSize

# استيراد صفحات التطبيق
from pos_page import PosPage
from customers_page import CustomersPage
from inventory_page import InventoryPage
from suppliers_page import SuppliersPage
from employees_page import EmployeesPage
from expenses_page import ExpensesPage
from banks_page import BanksPage
from contracts_page import ContractsPage

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة المبيعات")
        self.setGeometry(100, 100, 1280, 720)
        
        # إنشاء اتصال قاعدة البيانات
        self.db = Database()
        
        self.setup_ui()

    def setup_ui(self):
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.setFont(QFont("Arial", 10))

        self.create_top_toolbar()

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        side_bar = self.create_side_bar()
        
        self.stacked_widget = QStackedWidget()
        
        # إضافة صفحة نقطة البيع
        self.pos_page = PosPage(self.db)
        self.stacked_widget.addWidget(self.pos_page)
        
        # إضافة صفحة العملاء
        self.customers_page = CustomersPage(self.db)
        self.stacked_widget.addWidget(self.customers_page)
        
        # إضافة صفحة المخزون
        self.inventory_page = InventoryPage(self.db)
        self.stacked_widget.addWidget(self.inventory_page)
        
        # إضافة صفحة الموردين
        self.suppliers_page = SuppliersPage(self.db)
        self.stacked_widget.addWidget(self.suppliers_page)
        
        # إضافة صفحة الموظفين
        self.employees_page = EmployeesPage(self.db)
        self.stacked_widget.addWidget(self.employees_page)
        
        # إضافة صفحة المصروفات
        self.expenses_page = ExpensesPage(self.db)
        self.stacked_widget.addWidget(self.expenses_page)
        
        # إضافة صفحة البنوك
        self.banks_page = BanksPage(self.db)
        self.stacked_widget.addWidget(self.banks_page)
        
        # إضافة صفحة العقود
        self.contracts_page = ContractsPage(self.db)
        self.stacked_widget.addWidget(self.contracts_page)
        
        # إضافة صفحات وهمية مؤقتة للأقسام الأخرى
        for i in range(4):  # تقليل العدد من 5 إلى 4 لأننا أضفنا صفحة جديدة
            page = QWidget()
            layout = QVBoxLayout(page)
            label = QFrame()
            label.setStyleSheet("background-color: white;")
            layout.addWidget(label)
            self.stacked_widget.addWidget(page)

        content_layout = QVBoxLayout()
        content_layout.addWidget(self.stacked_widget)
        
        main_view = QWidget()
        main_view.setLayout(content_layout)
        
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(side_bar)  # القائمة على اليمين (في RTL)
        splitter.addWidget(main_view)  # المحتوى على اليسار
        splitter.setSizes([150, 1130])

        main_layout.addWidget(splitter)
        
        side_bar.currentRowChanged.connect(self.stacked_widget.setCurrentIndex)
        side_bar.setCurrentRow(0)

    def create_top_toolbar(self):
        toolbar = QToolBar("الشريط العلوي")
        self.addToolBar(toolbar)
        toolbar.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        menu_file = self.menuBar().addMenu("ملف")
        action_exit = QAction("خروج", self)
        action_exit.triggered.connect(self.close)
        menu_file.addAction(action_exit)

        self.menuBar().addMenu("معلومات")
        self.menuBar().addMenu("حماية")
        self.menuBar().addMenu("اختصارات")
        self.menuBar().addMenu("مساعدة")
        self.menuBar().addMenu("تخصيص")

    def create_side_bar(self):
        side_bar = QListWidget()
        side_bar.setFlow(QListWidget.Flow.TopToBottom)
        side_bar.setIconSize(QSize(40, 40))
        side_bar.setSpacing(5)
        side_bar.setMaximumWidth(150)
        side_bar.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        side_bar.setStyleSheet("""
            QListWidget { background-color: #2c3e50; color: white; font-size: 14px; }
            QListWidget::item { padding: 10px 5px; border-bottom: 1px solid #34495e; }
            QListWidget::item:selected { background-color: #3498db; border-right: 3px solid #2980b9; }
            QListWidget::item:hover { background-color: #34495e; }
        """)

        nav_items = [
            ("نقطة بيع", None),
            ("العملاء", None),
            ("المخزن", None),
            ("الموردين", None),
            ("الموظفين", None),
            ("المصروفات", None),
            ("البنوك", None),
            ("العقود", None),
            ("الديون والاقساط", None),
            ("الفواتير", None),
            ("تقارير مالية", None),
            ("اعدادات", None)
        ]

        for name, icon_path in nav_items:
            item = QListWidgetItem(name)
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            side_bar.addItem(item)
            
        return side_bar

    def closeEvent(self, event):
        """إغلاق اتصال قاعدة البيانات عند إغلاق التطبيق"""
        if hasattr(self, 'db'):
            self.db.close()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 