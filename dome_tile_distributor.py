#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت توزيع القرميد على القبة
يقوم بتوزيع القرميد على سطح القبة بنمط دائري واقعي
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
import math
import random

class DomeTileDistributor:
    """فئة توزيع القرميد على القبة"""
    
    def __init__(self, dome_name="القبة", tile_name="قرميد"):
        """تهيئة الموزع"""
        self.dome_name = dome_name
        self.tile_name = tile_name
        self.dome = None
        self.tile_template = None
        self.tiles_collection = None
        
    def setup_objects(self):
        """إعداد الكائنات المطلوبة"""
        self.dome = bpy.data.objects.get(self.dome_name)
        self.tile_template = bpy.data.objects.get(self.tile_name)
        
        if not self.dome:
            print(f"خطأ: لم يتم العثور على كائن القبة '{self.dome_name}'")
            return False
            
        if not self.tile_template:
            print(f"خطأ: لم يتم العثور على كائن القرميد '{self.tile_name}'")
            return False
            
        # إنشاء مجموعة للقرميد
        if "Tiles_Collection" in bpy.data.collections:
            self.tiles_collection = bpy.data.collections["Tiles_Collection"]
            # حذف القرميد الموجود
            for obj in list(self.tiles_collection.objects):
                bpy.data.objects.remove(obj, do_unlink=True)
        else:
            self.tiles_collection = bpy.data.collections.new("Tiles_Collection")
            bpy.context.scene.collection.children.link(self.tiles_collection)
            
        # إخفاء القرميد الأصلي
        self.tile_template.hide_set(True)
        
        return True
    
    def distribute_tiles(self):
        """توزيع القرميد على القبة"""
        if not self.setup_objects():
            return
            
        # معاملات التوزيع
        dome_radius = max(self.dome.dimensions.x, self.dome.dimensions.y) / 2
        dome_center = self.dome.location.copy()
        dome_height = self.dome.dimensions.z
        
        # حساب عدد الصفوف
        tile_height = self.tile_template.dimensions.y
        num_rows = int(dome_height / (tile_height * 0.7))
        
        print(f"نصف قطر القبة: {dome_radius:.2f}")
        print(f"ارتفاع القبة: {dome_height:.2f}")
        print(f"عدد الصفوف: {num_rows}")
        
        tile_count = 0
        
        # توزيع القرميد في صفوف
        for row in range(num_rows):
            height_ratio = row / (num_rows - 1) if num_rows > 1 else 0
            current_height = height_ratio * dome_height * 0.8
            height_from_center = current_height - dome_height * 0.4
            
            if abs(height_from_center) < dome_radius:
                current_radius = math.sqrt(max(0, dome_radius**2 - height_from_center**2))
                
                # حساب عدد القرميد في الصف
                circumference = 2 * math.pi * current_radius
                tiles_in_row = max(6, int(circumference / (self.tile_template.dimensions.x * 0.8)))
                
                angle_step = 2 * math.pi / tiles_in_row
                row_offset = (angle_step / 2) if row % 2 == 1 else 0
                
                for i in range(tiles_in_row):
                    angle = i * angle_step + row_offset
                    
                    # حساب الموقع
                    x = dome_center.x + current_radius * math.cos(angle)
                    y = dome_center.y + current_radius * math.sin(angle)
                    z = dome_center.z + current_height
                    
                    # إنشاء نسخة من القرميد
                    tile_copy = self.tile_template.copy()
                    tile_copy.data = self.tile_template.data.copy()
                    tile_copy.name = f"Tile_Row{row:02d}_{i:03d}"
                    
                    # تحديد الموقع والدوران
                    tile_copy.location = (x, y, z)
                    
                    radial_angle = math.atan2(y - dome_center.y, x - dome_center.x)
                    slope_angle = math.atan2(current_height, current_radius) * 0.5
                    tile_copy.rotation_euler = (slope_angle, 0, radial_angle + math.pi/2)
                    
                    # إضافة إلى المجموعة
                    self.tiles_collection.objects.link(tile_copy)
                    tile_copy.hide_set(False)
                    
                    tile_count += 1
        
        print(f"تم إنشاء {tile_count} قرميدة على القبة")
        return tile_count
    
    def add_variations(self):
        """إضافة تنويع واقعي للقرميد"""
        if not self.tiles_collection:
            return
            
        random.seed(42)  # للحصول على نتائج ثابتة
        variation_count = 0
        
        for obj in self.tiles_collection.objects:
            if obj.name.startswith("Tile_"):
                # تنويع في الموقع
                obj.location.x += random.uniform(-0.02, 0.02)
                obj.location.y += random.uniform(-0.02, 0.02)
                obj.location.z += random.uniform(-0.01, 0.01)
                
                # تنويع في الدوران
                obj.rotation_euler.z += random.uniform(-0.1, 0.1)
                
                # تنويع في المقياس
                scale_variation = random.uniform(0.95, 1.05)
                obj.scale = (scale_variation, scale_variation, scale_variation)
                
                variation_count += 1
        
        print(f"تم إضافة تنويع واقعي لـ {variation_count} قرميدة")
    
    def add_center_tile(self):
        """إضافة قرميدة مركزية في أعلى القبة"""
        if not all([self.dome, self.tile_template, self.tiles_collection]):
            return
            
        center_tile = self.tile_template.copy()
        center_tile.data = self.tile_template.data.copy()
        center_tile.name = "Center_Tile"
        
        # وضعها في أعلى القبة
        center_tile.location = (
            self.dome.location.x,
            self.dome.location.y,
            self.dome.location.z + self.dome.dimensions.z * 0.8
        )
        
        center_tile.scale = (1.2, 1.2, 1.2)
        
        self.tiles_collection.objects.link(center_tile)
        center_tile.hide_set(False)
        
        print("تم إضافة القرميدة المركزية")
    
    def distribute_complete(self):
        """تنفيذ التوزيع الكامل"""
        print("بدء توزيع القرميد على القبة...")
        
        tile_count = self.distribute_tiles()
        if tile_count > 0:
            self.add_variations()
            self.add_center_tile()
            print("تم الانتهاء من توزيع القرميد بنجاح!")
        else:
            print("فشل في توزيع القرميد")

# دالة سريعة للاستخدام
def distribute_tiles_on_dome(dome_name="القبة", tile_name="قرميد"):
    """دالة سريعة لتوزيع القرميد على القبة"""
    distributor = DomeTileDistributor(dome_name, tile_name)
    distributor.distribute_complete()

# تنفيذ التوزيع إذا تم تشغيل السكريبت مباشرة
if __name__ == "__main__":
    distribute_tiles_on_dome()
