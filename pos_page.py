from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QTableView, QHeaderView,
    QMessageBox, QLabel, QLineEdit, QSplitter, QGroupBox, QFormLayout,
    QSpinBox, QDoubleSpinBox, QRadioButton, QComboBox, QDateEdit
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QStandardItemModel, QStandardItem, QColor

class PosPage(QWidget):
    """
    واجهة قسم نقطة البيع
    """
    def __init__(self, db=None):
        super().__init__()
        self.db = db
        # متغير لتخزين أصناف السلة الحالية
        self.cart_items = {}
        self.setup_ui()
        self.connect_signals()
        self.reset_invoice()

    def setup_ui(self):
        """
        دالة إعداد واجهة المستخدم
        """
        main_layout = QHBoxLayout(self)

        # استخدام QSplitter لتقسيم الواجهة إلى قسمين
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # إنشاء الجانب الأيسر (الفاتورة) والجانب الأيمن (بحث المنتجات)
        invoice_widget = self.create_invoice_widget()
        product_search_widget = self.create_product_search_widget()

        # إضافة الويدجت إلى الـ splitter
        # في تخطيط RTL، الويدجت الأول يظهر على اليمين
        splitter.addWidget(product_search_widget)
        splitter.addWidget(invoice_widget)
        
        # تعيين نسبة التقسيم الأولية (مثلاً، 60% للفاتورة و 40% للبحث)
        splitter.setSizes([int(self.width() * 0.4), int(self.width() * 0.6)])

        main_layout.addWidget(splitter)

    def create_product_search_widget(self):
        """
        دالة لإنشاء الجزء الأيمن (بحث المنتجات)
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 1. صندوق البحث
        search_group = QGroupBox("البحث عن منتج")
        search_layout = QVBoxLayout(search_group)
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("أدخل اسم المنتج, الباركود, الفئة...")
        search_layout.addWidget(self.search_edit)
        
        # 2. جدول نتائج البحث
        self.search_results_table = QTableView()
        self.search_results_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.search_results_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        search_layout.addWidget(self.search_results_table)

        # 3. قسم الإضافة للسلة
        add_to_cart_group = QGroupBox("إضافة إلى السلة")
        add_to_cart_layout = QHBoxLayout(add_to_cart_group)
        
        add_to_cart_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setRange(1, 999)
        add_to_cart_layout.addWidget(self.quantity_spinbox)
        
        self.add_to_cart_button = QPushButton("أضف للسلة")
        add_to_cart_layout.addWidget(self.add_to_cart_button)

        layout.addWidget(search_group)
        layout.addWidget(add_to_cart_group)
        
        # إعداد نموذج البيانات لجدول البحث
        self.search_model = QStandardItemModel()
        self.search_model.setHorizontalHeaderLabels(["الباركود", "الاسم", "السعر", "الكمية المتاحة"])
        self.search_results_table.setModel(self.search_model)
        self.search_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        return widget

    def create_invoice_widget(self):
        """
        دالة لإنشاء الجزء الأيسر (الفاتورة)
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 1. جدول عرض أصناف الفاتورة (السلة)
        cart_group = QGroupBox("الفاتورة الحالية")
        cart_layout = QVBoxLayout(cart_group)
        self.cart_table = QTableView()
        self.cart_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        cart_layout.addWidget(self.cart_table)
        
        # نموذج مبدئي لجدول السلة
        self.cart_model = QStandardItemModel()
        self.cart_model.setHorizontalHeaderLabels(["المعرف", "الصنف", "الكمية", "السعر", "الإجمالي"])
        self.cart_table.setModel(self.cart_model)
        # إخفاء عمود المعرف
        self.cart_table.setColumnHidden(0, True)
        
        # 2. تفاصيل الفاتورة
        details_group = QGroupBox("تفاصيل الدفع")
        details_layout = QFormLayout(details_group)

        self.total_label = QLabel("0.00")
        self.discount_spinbox = QDoubleSpinBox()
        self.discount_spinbox.setRange(0, 10000)
        self.discount_spinbox.setSingleStep(10)
        self.final_total_label = QLabel("0.00")
        
        details_layout.addRow("الإجمالي:", self.total_label)
        details_layout.addRow("الخصم:", self.discount_spinbox)
        details_layout.addRow("الإجمالي النهائي:", self.final_total_label)

        # 3. خيارات البيع
        payment_group = QGroupBox("خيارات البيع")
        payment_layout = QVBoxLayout(payment_group)
        
        # أزرار اختيار طريقة الدفع
        payment_method_layout = QHBoxLayout()
        self.cash_radio = QRadioButton("نقدي")
        self.credit_radio = QRadioButton("آجل")
        self.installments_radio = QRadioButton("أقساط")
        self.cash_radio.setChecked(True)
        payment_method_layout.addWidget(self.cash_radio)
        payment_method_layout.addWidget(self.credit_radio)
        payment_method_layout.addWidget(self.installments_radio)
        payment_layout.addLayout(payment_method_layout)
        
        # قائمة العملاء (تظهر للآجل والأقساط)
        self.customer_label = QLabel("العميل:")
        self.customer_combo = QComboBox()
        self.customer_label.setVisible(False)
        self.customer_combo.setVisible(False) # إخفاء مبدئي
        payment_layout.addWidget(self.customer_label)
        payment_layout.addWidget(self.customer_combo)

        # 4. أزرار إتمام البيع
        complete_sale_layout = QHBoxLayout()
        self.complete_sale_button = QPushButton("إتمام البيع")
        self.new_invoice_button = QPushButton("فاتورة جديدة")
        complete_sale_layout.addWidget(self.complete_sale_button)
        complete_sale_layout.addWidget(self.new_invoice_button)

        layout.addWidget(cart_group)
        layout.addWidget(details_group)
        layout.addWidget(payment_group)
        layout.addLayout(complete_sale_layout)

        return widget

    def connect_signals(self):
        """
        دالة لربط كل الإشارات (signals) بالوظائف (slots)
        """
        self.search_edit.textChanged.connect(self.search_products)
        self.add_to_cart_button.clicked.connect(self.add_product_to_cart)
        self.discount_spinbox.valueChanged.connect(self.update_totals)
        self.new_invoice_button.clicked.connect(self.reset_invoice)
        self.complete_sale_button.clicked.connect(self.complete_sale)

        # ربط أزرار طريقة الدفع بإظهار/إخفاء قائمة العملاء
        self.cash_radio.toggled.connect(self.toggle_customer_selection)
        self.credit_radio.toggled.connect(self.toggle_customer_selection)
        self.installments_radio.toggled.connect(self.toggle_customer_selection)
    
    def search_products(self):
        """
        البحث عن المنتجات وعرض النتائج
        """
        search_term = self.search_edit.text().lower()
        
        # في هذه النسخة البسيطة، نقوم بتصفية المنتجات المعروضة فقط
        for row in range(self.search_model.rowCount()):
            barcode = self.search_model.item(row, 0).text().lower()
            name = self.search_model.item(row, 1).text().lower()
            
            if search_term in barcode or search_term in name:
                self.search_results_table.setRowHidden(row, False)
            else:
                self.search_results_table.setRowHidden(row, True)
            
    def add_product_to_cart(self):
        """
        إضافة المنتج المحدد إلى سلة المشتريات
        """
        selected_rows = self.search_results_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد منتج لإضافته.", QMessageBox.StandardButton.Ok)
            return

        row = selected_rows[0].row()
        barcode = self.search_model.item(row, 0).text()
        product_name = self.search_model.item(row, 1).text()
        price = float(self.search_model.item(row, 2).text())
        available_qty = int(self.search_model.item(row, 3).text())
        requested_qty = self.quantity_spinbox.value()

        if requested_qty > available_qty:
            QMessageBox.warning(self, "كمية غير كافية", f"الكمية المتاحة من '{product_name}' هي {available_qty} فقط.", QMessageBox.StandardButton.Ok)
            return

        # إذا كان الصنف موجوداً بالفعل في السلة، قم بزيادة الكمية
        if barcode in self.cart_items:
            current_qty = int(self.cart_items[barcode]["quantity"])
            new_qty = current_qty + requested_qty
            
            if new_qty > available_qty:
                QMessageBox.warning(self, "كمية غير كافية", f"إجمالي الكمية المطلوبة ({new_qty}) يتجاوز المتاح ({available_qty}).", QMessageBox.StandardButton.Ok)
                return
            
            self.cart_items[barcode]["quantity"] = new_qty
            self.cart_items[barcode]["total"] = new_qty * price
            
            # تحديث العرض
            for cart_row in range(self.cart_model.rowCount()):
                if self.cart_model.item(cart_row, 0).text() == barcode:
                    self.cart_model.item(cart_row, 2).setText(str(new_qty))
                    self.cart_model.item(cart_row, 4).setText(f"{new_qty * price:.2f}")
                    break

        else: # إضافة صنف جديد للسلة
            item_id = QStandardItem(barcode)
            item_name = QStandardItem(product_name)
            item_qty = QStandardItem(str(requested_qty))
            item_price = QStandardItem(f"{price:.2f}")
            item_total = QStandardItem(f"{requested_qty * price:.2f}")

            # جعل العناصر غير قابلة للتعديل
            for item in [item_id, item_name, item_qty, item_price, item_total]:
                item.setEditable(False)

            row_items = [item_id, item_name, item_qty, item_price, item_total]
            self.cart_model.appendRow(row_items)
            
            self.cart_items[barcode] = {
                "name": product_name,
                "price": price,
                "quantity": requested_qty,
                "total": requested_qty * price
            }

        self.update_totals()

    def update_totals(self):
        """
        حساب وتحديث إجماليات الفاتورة
        """
        total = sum(item["total"] for item in self.cart_items.values())
        
        self.total_label.setText(f"{total:.2f}")
        
        discount = self.discount_spinbox.value()
        final_total = total - discount
        
        # تلوين الإجمالي النهائي إذا كان الخصم مطبقاً
        color = "red" if discount > 0 else "black"
        self.final_total_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        self.final_total_label.setText(f"{final_total:.2f}")

    def reset_invoice(self):
        """
        إعادة تعيين الفاتورة لحالة البداية
        """
        self.cart_model.clear()
        self.cart_model.setHorizontalHeaderLabels(["المعرف", "الصنف", "الكمية", "السعر", "الإجمالي"])
        self.cart_table.setColumnHidden(0, True)
        self.cart_items = {}
        self.discount_spinbox.setValue(0.0)
        self.update_totals()
        self.search_edit.clear()
        self.cash_radio.setChecked(True)

    def toggle_customer_selection(self):
        """
        إظهار أو إخفاء قائمة العملاء بناءً على طريقة الدفع
        """
        show_customer = self.credit_radio.isChecked() or self.installments_radio.isChecked()
        self.customer_label.setVisible(show_customer)
        self.customer_combo.setVisible(show_customer)
        if show_customer:
            self.load_customers()

    def load_customers(self):
        """
        تحميل قائمة العملاء في القائمة المنسدلة
        """
        if self.customer_combo.count() > 0:
            return 
        
        self.customer_combo.clear()
        self.customer_combo.addItem("- اختر عميلاً -", None)
        
        # إضافة عملاء وهميين للعرض
        customers = [
            {"id": 1, "name": "عميل 1"},
            {"id": 2, "name": "عميل 2"},
            {"id": 3, "name": "عميل 3"}
        ]
        
        for customer in customers:
            self.customer_combo.addItem(customer["name"], customer["id"])

    def complete_sale(self):
        """
        إتمام عملية البيع وحفظ الفاتورة
        """
        # 1. التحقق من البيانات
        if self.cart_model.rowCount() == 0:
            QMessageBox.warning(self, "خطأ", "سلة المشتريات فارغة!", QMessageBox.StandardButton.Ok)
            return

        payment_method = "نقدي"
        customer_id = None
        if self.credit_radio.isChecked():
            payment_method = "آجل"
        elif self.installments_radio.isChecked():
            payment_method = "أقساط"

        if payment_method in ["آجل", "أقساط"]:
            customer_id = self.customer_combo.currentData()
            if not customer_id:
                QMessageBox.warning(self, "خطأ", "الرجاء اختيار عميل للفواتير الآجلة أو الأقساط.", QMessageBox.StandardButton.Ok)
                return

        # 2. عرض رسالة نجاح (في التطبيق الكامل سيتم حفظ البيانات في قاعدة البيانات)
        total_amount = float(self.total_label.text())
        discount = self.discount_spinbox.value()
        final_amount = float(self.final_total_label.text())
        
        message = f"""
        تم إتمام عملية البيع بنجاح!
        
        طريقة الدفع: {payment_method}
        المبلغ الإجمالي: {total_amount:.2f}
        الخصم: {discount:.2f}
        المبلغ النهائي: {final_amount:.2f}
        """
        
        if customer_id:
            message += f"\nالعميل: {self.customer_combo.currentText()}"
        
        QMessageBox.information(self, "نجاح", message, QMessageBox.StandardButton.Ok)
        
        # 3. إعادة تعيين الفاتورة
        self.reset_invoice() 